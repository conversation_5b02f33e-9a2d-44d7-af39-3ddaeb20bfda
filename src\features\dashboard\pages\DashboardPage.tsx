import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  <PERSON>, 
  Wrench, 
  AlertTriangle, 
  DollarSign, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  XCircle, 
  Calendar,
  FileText,
  Bell,
  Download,
  RefreshCw,
  ArrowRight,
  Fuel,
  Shield,
  BarChart3,
  <PERSON><PERSON><PERSON>
} from 'lucide-react';

interface DashboardStats {
  totalVehicles: number;
  activeVehicles: number;
  inMaintenance: number;
  outOfService: number;
  pendingWorkOrders: number;
  overdueWorkOrders: number;
  monthlySpend: number;
  budgetUtilization: number;
  fuelEfficiency: number;
  complianceScore: number;
}

interface RecentActivity {
  id: string;
  type: 'work_order' | 'vehicle' | 'maintenance' | 'alert';
  title: string;
  description: string;
  timestamp: string;
  priority?: 'Low' | 'Medium' | 'High' | 'Critical';
  status?: string;
}

interface Alert {
  id: string;
  type: 'critical' | 'warning' | 'info';
  title: string;
  description: string;
  vehicleId?: string;
  actionRequired: boolean;
  timestamp: string;
}

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const [timeRange, setTimeRange] = useState('30d');
  const [refreshing, setRefreshing] = useState(false);

  // Mock data - replace with API calls
  const stats: DashboardStats = {
    totalVehicles: 247,
    activeVehicles: 231,
    inMaintenance: 12,
    outOfService: 4,
    pendingWorkOrders: 18,
    overdueWorkOrders: 3,
    monthlySpend: 485000,
    budgetUtilization: 67,
    fuelEfficiency: 8.2,
    complianceScore: 94
  };

  const recentActivity: RecentActivity[] = [
    {
      id: '1',
      type: 'work_order',
      title: 'New Work Order Created',
      description: 'Brake pad replacement for GP 123 ABC',
      timestamp: '2024-01-15T10:30:00Z',
      priority: 'High',
      status: 'Pending'
    },
    {
      id: '2',
      type: 'maintenance',
      title: 'Scheduled Service Completed',
      description: 'GP 456 DEF - 15,000km service completed',
      timestamp: '2024-01-15T09:15:00Z',
      status: 'Completed'
    },
    {
      id: '3',
      type: 'vehicle',
      title: 'Vehicle Added',
      description: 'New Toyota Hilux added to fleet',
      timestamp: '2024-01-15T08:45:00Z'
    },
    {
      id: '4',
      type: 'alert',
      title: 'Compliance Alert',
      description: 'GP 789 GHI license renewal due in 7 days',
      timestamp: '2024-01-15T08:00:00Z',
      priority: 'Medium'
    }
  ];

  const alerts: Alert[] = [
    {
      id: '1',
      type: 'critical',
      title: 'Vehicle Breakdown',
      description: 'GP 123 ABC reported engine failure - immediate attention required',
      vehicleId: '1',
      actionRequired: true,
      timestamp: '2024-01-15T11:00:00Z'
    },
    {
      id: '2',
      type: 'warning',
      title: 'Overdue Maintenance',
      description: '3 vehicles have overdue scheduled maintenance',
      actionRequired: true,
      timestamp: '2024-01-15T10:00:00Z'
    },
    {
      id: '3',
      type: 'warning',
      title: 'Budget Alert',
      description: 'Maintenance budget 85% utilized for this month',
      actionRequired: false,
      timestamp: '2024-01-15T09:30:00Z'
    },
    {
      id: '4',
      type: 'info',
      title: 'License Renewals',
      description: '5 vehicle licenses expiring within 30 days',
      actionRequired: true,
      timestamp: '2024-01-15T09:00:00Z'
    }
  ];

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'work_order': return <Wrench className="h-4 w-4 text-blue-600" />;
      case 'vehicle': return <Car className="h-4 w-4 text-green-600" />;
      case 'maintenance': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'alert': return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      default: return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      default:
        return <Bell className="h-4 w-4 text-blue-600" />;
    }
  };

  const getAlertBgColor = (type: string) => {
    switch (type) {
      case 'critical':
        return 'border-red-200 bg-red-50';
      case 'warning':
        return 'border-orange-200 bg-orange-50';
      default:
        return 'border-blue-200 bg-blue-50';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  return (
    <div className="p-3 sm:p-4 lg:p-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Fleet Dashboard</h1>
          <p className="text-sm sm:text-base text-gray-600">Overview of your fleet operations</p>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm h-10 sm:h-auto"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 h-10 text-sm sm:text-base"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            <span className="hidden sm:inline">Refresh</span>
            <span className="sm:hidden">Refresh</span>
          </button>
        </div>
      </div>

      {/* Critical Alerts */}
      {alerts.filter(alert => alert.type === 'critical').length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 sm:p-4">
          <div className="flex items-center space-x-2 mb-3">
            <XCircle className="h-5 w-5 text-red-600 flex-shrink-0" />
            <h3 className="font-semibold text-red-900 text-sm sm:text-base">Critical Alerts Require Immediate Attention</h3>
          </div>
          <div className="space-y-2">
            {alerts.filter(alert => alert.type === 'critical').map(alert => (
              <div key={alert.id} className="flex flex-col sm:flex-row sm:items-center sm:justify-between bg-white rounded p-3 gap-3">
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-red-900 text-sm sm:text-base">{alert.title}</p>
                  <p className="text-xs sm:text-sm text-red-700 break-words">{alert.description}</p>
                </div>
                <button className="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded text-xs sm:text-sm transition-colors flex-shrink-0 w-full sm:w-auto">
                  Take Action
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="text-xs sm:text-sm font-medium text-gray-600">Total Vehicles</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-900">{stats.totalVehicles}</p>
              <div className="flex items-center mt-1 sm:mt-2">
                <span className="text-xs sm:text-sm text-green-600 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +2.3%
                </span>
                <span className="text-xs sm:text-sm text-gray-500 ml-2">vs last month</span>
              </div>
            </div>
            <div className="p-2 sm:p-3 bg-blue-100 rounded-lg flex-shrink-0">
              <Car className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="text-xs sm:text-sm font-medium text-gray-600">Active Vehicles</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-900">{stats.activeVehicles}</p>
              <div className="flex items-center mt-1 sm:mt-2">
                <span className="text-xs sm:text-sm text-gray-600">
                  {Math.round((stats.activeVehicles / stats.totalVehicles) * 100)}% operational
                </span>
              </div>
            </div>
            <div className="p-2 sm:p-3 bg-green-100 rounded-lg flex-shrink-0">
              <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="text-xs sm:text-sm font-medium text-gray-600">Pending Work Orders</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-900">{stats.pendingWorkOrders}</p>
              <div className="flex items-center mt-1 sm:mt-2">
                <span className="text-xs sm:text-sm text-orange-600">
                  {stats.overdueWorkOrders} overdue
                </span>
              </div>
            </div>
            <div className="p-2 sm:p-3 bg-orange-100 rounded-lg flex-shrink-0">
              <Wrench className="h-5 w-5 sm:h-6 sm:w-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="text-xs sm:text-sm font-medium text-gray-600">Monthly Spend</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-900">{formatCurrency(stats.monthlySpend)}</p>
              <div className="flex items-center mt-1 sm:mt-2">
                <span className="text-xs sm:text-sm text-red-600 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +12.5%
                </span>
                <span className="text-xs sm:text-sm text-gray-500 ml-2">vs budget</span>
              </div>
            </div>
            <div className="p-2 sm:p-3 bg-red-100 rounded-lg flex-shrink-0">
              <DollarSign className="h-5 w-5 sm:h-6 sm:w-6 text-red-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Secondary Metrics */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4">
          <div className="flex items-center space-x-2 sm:space-x-3">
            <div className="p-1.5 sm:p-2 bg-blue-100 rounded flex-shrink-0">
              <Fuel className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm text-gray-600">Fuel Efficiency</p>
              <p className="text-sm sm:text-lg font-semibold text-gray-900">{stats.fuelEfficiency}L/100km</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4">
          <div className="flex items-center space-x-2 sm:space-x-3">
            <div className="p-1.5 sm:p-2 bg-green-100 rounded flex-shrink-0">
              <Shield className="h-3 w-3 sm:h-4 sm:w-4 text-green-600" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm text-gray-600">Compliance Score</p>
              <p className="text-sm sm:text-lg font-semibold text-gray-900">{stats.complianceScore}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4">
          <div className="flex items-center space-x-2 sm:space-x-3">
            <div className="p-1.5 sm:p-2 bg-yellow-100 rounded flex-shrink-0">
              <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-yellow-600" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm text-gray-600">In Maintenance</p>
              <p className="text-sm sm:text-lg font-semibold text-gray-900">{stats.inMaintenance}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4">
          <div className="flex items-center space-x-2 sm:space-x-3">
            <div className="p-1.5 sm:p-2 bg-red-100 rounded flex-shrink-0">
              <XCircle className="h-3 w-3 sm:h-4 sm:w-4 text-red-600" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm text-gray-600">Out of Service</p>
              <p className="text-sm sm:text-lg font-semibold text-gray-900">{stats.outOfService}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Charts Section */}
        <div className="lg:col-span-2 space-y-4 sm:space-y-6">
          {/* Fleet Status Chart */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-2">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900">Fleet Status Overview</h3>
              <button className="text-blue-600 hover:text-blue-800 text-xs sm:text-sm self-start sm:self-auto">
                View Details
              </button>
            </div>
            <div className="h-48 sm:h-64 flex items-center justify-center bg-gray-50 rounded-lg">
              <div className="text-center">
                <BarChart3 className="h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-sm sm:text-base text-gray-500">Fleet Status Chart</p>
                <p className="text-xs sm:text-sm text-gray-400">Chart component would be rendered here</p>
              </div>
            </div>
          </div>

          {/* Cost Breakdown */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-2">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900">Monthly Cost Breakdown</h3>
              <button className="text-blue-600 hover:text-blue-800 text-xs sm:text-sm self-start sm:self-auto">
                View Full Report
              </button>
            </div>
            <div className="h-48 sm:h-64 flex items-center justify-center bg-gray-50 rounded-lg">
              <div className="text-center">
                <PieChart className="h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-sm sm:text-base text-gray-500">Cost Breakdown Chart</p>
                <p className="text-xs sm:text-sm text-gray-400">Chart component would be rendered here</p>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-4 sm:space-y-6 flex flex-col">
          {/* Alerts */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 flex-1">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-2">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900">Alerts</h3>
              <button
                onClick={() => navigate('/alerts')}
                className="text-blue-600 hover:text-blue-800 text-xs sm:text-sm flex items-center space-x-1 self-start sm:self-auto"
              >
                <span>View All</span>
                <ArrowRight className="h-3 w-3" />
              </button>
            </div>
            <div className="space-y-2 sm:space-y-3">
              {alerts.slice(0, 4).map(alert => (
                <div key={alert.id} className={`p-2 sm:p-3 rounded-lg border ${getAlertBgColor(alert.type)}`}>
                  <div className="flex items-start space-x-2 sm:space-x-3">
                    <div className="flex-shrink-0 mt-0.5">
                      {getAlertIcon(alert.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-xs sm:text-sm font-medium text-gray-900 leading-tight">{alert.title}</p>
                      <p className="text-xs sm:text-sm text-gray-600 truncate mt-1">{alert.description}</p>
                      <p className="text-xs text-gray-500 mt-1">{formatTimeAgo(alert.timestamp)}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 flex-1">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-2">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900">Recent Activity</h3>
              <button
                onClick={() => navigate('/activity')}
                className="text-blue-600 hover:text-blue-800 text-xs sm:text-sm flex items-center space-x-1 self-start sm:self-auto"
              >
                <span>View All</span>
                <ArrowRight className="h-3 w-3" />
              </button>
            </div>
            <div className="space-y-2 sm:space-y-3">
              {recentActivity.slice(0, 5).map(activity => (
                <div key={activity.id} className="flex items-start space-x-2 sm:space-x-3">
                  <div className="p-1 bg-gray-100 rounded flex-shrink-0 mt-0.5">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-xs sm:text-sm font-medium text-gray-900 leading-tight">{activity.title}</p>
                    <p className="text-xs sm:text-sm text-gray-600 truncate mt-1">{activity.description}</p>
                    <p className="text-xs text-gray-500 mt-1">{formatTimeAgo(activity.timestamp)}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Upcoming Tasks & Deadlines */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-2">
          <h3 className="text-base sm:text-lg font-semibold text-gray-900">Upcoming Tasks & Deadlines</h3>
          <button
            onClick={() => navigate('/tasks')}
            className="text-blue-600 hover:text-blue-800 text-xs sm:text-sm flex items-center space-x-1 self-start sm:self-auto"
          >
            <span className="hidden sm:inline">View All Tasks</span>
            <span className="sm:hidden">View All</span>
            <ArrowRight className="h-3 w-3" />
          </button>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
          <div className="border border-orange-200 bg-orange-50 rounded-lg p-3 sm:p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Calendar className="h-4 w-4 text-orange-600 flex-shrink-0" />
              <span className="text-xs sm:text-sm font-medium text-orange-900">Due This Week</span>
            </div>
            <p className="text-xl sm:text-2xl font-bold text-orange-900">7</p>
            <p className="text-xs sm:text-sm text-orange-700">Scheduled maintenance tasks</p>
          </div>

          <div className="border border-red-200 bg-red-50 rounded-lg p-3 sm:p-4">
            <div className="flex items-center space-x-2 mb-2">
              <AlertTriangle className="h-4 w-4 text-red-600 flex-shrink-0" />
              <span className="text-xs sm:text-sm font-medium text-red-900">Overdue</span>
            </div>
            <p className="text-xl sm:text-2xl font-bold text-red-900">3</p>
            <p className="text-xs sm:text-sm text-red-700">Tasks requiring immediate attention</p>
          </div>

          <div className="border border-blue-200 bg-blue-50 rounded-lg p-3 sm:p-4 sm:col-span-2 lg:col-span-1">
            <div className="flex items-center space-x-2 mb-2">
              <CheckCircle className="h-4 w-4 text-blue-600 flex-shrink-0" />
              <span className="text-xs sm:text-sm font-medium text-blue-900">Completed Today</span>
            </div>
            <p className="text-xl sm:text-2xl font-bold text-blue-900">12</p>
            <p className="text-xs sm:text-sm text-blue-700">Tasks completed successfully</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;

