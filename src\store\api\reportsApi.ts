import { api } from '../api';

export interface ReportFilters {
  date_from: string;
  date_to: string;
  department?: string;
  vehicle_ids?: string[];
}

export interface FleetUtilizationReport {
  total_vehicles: number;
  active_vehicles: number;
  utilization_rate: number;
  average_mileage: number;
  by_department: Record<string, {
    vehicles: number;
    utilization: number;
    mileage: number;
  }>;
}

export interface MaintenanceCostReport {
  total_cost: number;
  average_cost_per_vehicle: number;
  cost_by_category: Record<string, number>;
  cost_by_month: Array<{
    month: string;
    cost: number;
  }>;
  top_expensive_vehicles: Array<{
    vehicle_id: string;
    registration: string;
    cost: number;
  }>;
}

export const reportsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Fleet utilization report
    getFleetUtilizationReport: builder.query<FleetUtilizationReport, ReportFilters>({
      query: (filters) => ({
        url: '/reports/fleet-utilization',
        params: filters,
      }),
    }),

    // Maintenance cost report
    getMaintenanceCostReport: builder.query<MaintenanceCostReport, ReportFilters>({
      query: (filters) => ({
        url: '/reports/maintenance-costs',
        params: filters,
      }),
    }),

    // Export report
    exportReport: builder.mutation<{ download_url: string }, {
      report_type: string;
      format: 'pdf' | 'excel' | 'csv';
      filters: ReportFilters;
    }>({
      query: (data) => ({
        url: '/reports/export',
        method: 'POST',
        body: data,
      }),
    }),

    // Dashboard summary
    getDashboardSummary: builder.query<{
      total_vehicles: number;
      active_work_orders: number;
      pending_invoices: number;
      monthly_costs: number;
      alerts: Array<{
        type: string;
        message: string;
        severity: 'low' | 'medium' | 'high';
      }>;
    }, void>({
      query: () => '/reports/dashboard-summary',
    }),
  }),
});

export const {
  useGetFleetUtilizationReportQuery,
  useGetMaintenanceCostReportQuery,
  useExportReportMutation,
  useGetDashboardSummaryQuery,
} = reportsApi;