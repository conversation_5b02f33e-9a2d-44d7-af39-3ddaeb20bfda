import React, { useState } from 'react';
import { use<PERSON><PERSON>, Controller, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { 
  ChevronLeft, 
  ChevronRight, 
  Plus, 
  Trash2, 
  Calendar,
  Settings,
  FileText,
  Wrench,
  Shield
} from 'lucide-react';

// Enhanced schema with all required fields
const vehicleSchema = z.object({
  // Basic Information
  registrationNumber: z.string().min(1, 'Registration number is required'),
  make: z.string().min(1, 'Make is required'),
  model: z.string().min(1, 'Model is required'),
  year: z.number().min(1900, 'Year must be after 1900').max(new Date().getFullYear() + 1),
  vin: z.string().min(17, 'VIN must be 17 characters').max(17),
  engineNumber: z.string().min(1, 'Engine number is required'),
  fuelType: z.enum(['petrol', 'diesel', 'electric', 'hybrid']),
  category: z.enum(['sedan', 'suv', 'truck', 'van', 'motorcycle', 'bus']),
  
  // Status and Registration
  status: z.enum(['active', 'maintenance', 'retired', 'accident']),
  dateOfRegistration: z.string().min(1, 'Date of registration is required'),
  color: z.string().min(1, 'Vehicle color is required'),
  
  // Operational Details
  odometerReading: z.number().min(0, 'Odometer reading must be positive'),
  department: z.string().min(1, 'Department is required'),
  assignedDriver: z.string().optional(),
  location: z.string().min(1, 'Location is required'),
  
  // Financial Information
  purchaseDate: z.string().min(1, 'Purchase date is required'),
  purchasePrice: z.number().min(0, 'Purchase price must be positive'),
  insuranceExpiry: z.string().min(1, 'Insurance expiry is required'),
  licenseExpiry: z.string().min(1, 'License expiry is required'),
  
  // Service History
  serviceHistory: z.array(z.object({
    date: z.string().min(1, 'Service date is required'),
    type: z.string().min(1, 'Service type is required'),
    mileage: z.number().min(0, 'Mileage must be positive'),
    description: z.string().min(1, 'Description is required'),
    cost: z.number().min(0, 'Cost must be positive'),
    serviceProvider: z.string().min(1, 'Service provider is required'),
  })).optional(),
  
  // Parts and Serial Numbers
  parts: z.array(z.object({
    partName: z.string().min(1, 'Part name is required'),
    serialNumber: z.string().min(1, 'Serial number is required'),
    manufacturer: z.string().min(1, 'Manufacturer is required'),
    installationDate: z.string().min(1, 'Installation date is required'),
    partType: z.enum(['battery', 'engine', 'transmission', 'brakes', 'tires', 'electronics', 'other']),
  })).optional(),
  
  // Part Warranties
  warranties: z.array(z.object({
    partName: z.string().min(1, 'Part name is required'),
    warrantyProvider: z.string().min(1, 'Warranty provider is required'),
    startDate: z.string().min(1, 'Start date is required'),
    endDate: z.string().min(1, 'End date is required'),
    warrantyType: z.enum(['manufacturer', 'extended', 'service_provider']),
    coverageDetails: z.string().min(1, 'Coverage details are required'),
  })).optional(),
  
  // Maintenance Plans
  maintenancePlan: z.object({
    planType: z.enum(['basic', 'comprehensive', 'premium', 'custom']),
    serviceInterval: z.number().min(1, 'Service interval is required'),
    intervalType: z.enum(['kilometers', 'months']),
    nextServiceDate: z.string().min(1, 'Next service date is required'),
    nextServiceMileage: z.number().min(0, 'Next service mileage must be positive'),
    serviceProvider: z.string().min(1, 'Service provider is required'),
    estimatedCost: z.number().min(0, 'Estimated cost must be positive'),
  }),
  
  // Maintenance Schedule
  maintenanceSchedule: z.array(z.object({
    taskName: z.string().min(1, 'Task name is required'),
    frequency: z.number().min(1, 'Frequency is required'),
    frequencyType: z.enum(['kilometers', 'months']),
    lastCompleted: z.string().optional(),
    nextDue: z.string().min(1, 'Next due date is required'),
    priority: z.enum(['low', 'medium', 'high', 'critical']),
    estimatedDuration: z.number().min(1, 'Estimated duration is required'),
    estimatedCost: z.number().min(0, 'Estimated cost must be positive'),
  })).optional(),
});

export type VehicleFormData = z.infer<typeof vehicleSchema>;

interface VehicleFormProps {
  initialData?: Partial<VehicleFormData>;
  onSubmit: (data: VehicleFormData) => void;
  onCancel?: () => void;
  isLoading?: boolean;
  submitLabel?: string;
}

const STEPS = [
  { id: 1, title: 'Basic Information', icon: FileText },
  { id: 2, title: 'Status & Registration', icon: Calendar },
  { id: 3, title: 'Operational Details', icon: Settings },
  { id: 4, title: 'Service History', icon: Wrench },
  { id: 5, title: 'Parts & Warranties', icon: Shield },
  { id: 6, title: 'Maintenance Plans', icon: Calendar },
];

export const VehicleForm: React.FC<VehicleFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  submitLabel = 'Save Vehicle',
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  
  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    trigger,
  } = useForm<VehicleFormData>({
    resolver: zodResolver(vehicleSchema),
    defaultValues: {
      registrationNumber: '',
      make: '',
      model: '',
      year: new Date().getFullYear(),
      vin: '',
      engineNumber: '',
      fuelType: 'petrol',
      category: 'sedan',
      status: 'active',
      dateOfRegistration: '',
      color: '',
      odometerReading: 0,
      department: '',
      assignedDriver: '',
      location: '',
      purchaseDate: '',
      purchasePrice: 0,
      insuranceExpiry: '',
      licenseExpiry: '',
      serviceHistory: [],
      parts: [],
      warranties: [],
      maintenancePlan: {
        planType: 'basic',
        serviceInterval: 10000,
        intervalType: 'kilometers',
        nextServiceDate: '',
        nextServiceMileage: 0,
        serviceProvider: '',
        estimatedCost: 0,
      },
      maintenanceSchedule: [],
      ...initialData,
    },
  });

  const { fields: serviceFields, append: appendService, remove: removeService } = useFieldArray({
    control,
    name: 'serviceHistory',
  });

  const { fields: partFields, append: appendPart, remove: removePart } = useFieldArray({
    control,
    name: 'parts',
  });

  const { fields: warrantyFields, append: appendWarranty, remove: removeWarranty } = useFieldArray({
    control,
    name: 'warranties',
  });

  // Add this if you need to use maintenanceSchedule fields
  // const { fields: scheduleFields, append: appendSchedule, remove: removeSchedule } = useFieldArray({
  //   control,
  //   name: 'maintenanceSchedule',
  // });

  const progress = (currentStep / STEPS.length) * 100;

  const nextStep = async () => {
    const fieldsToValidate = getFieldsForStep(currentStep);
    const isValid = await trigger(fieldsToValidate);
    
    if (isValid && currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const getFieldsForStep = (step: number): (keyof VehicleFormData)[] => {
    switch (step) {
      case 1:
        return ['registrationNumber', 'make', 'model', 'year', 'vin', 'engineNumber', 'fuelType', 'category'];
      case 2:
        return ['status', 'dateOfRegistration', 'color', 'odometerReading', 'insuranceExpiry', 'licenseExpiry'];
      case 3:
        return ['department', 'location', 'purchaseDate', 'purchasePrice'];
      case 4:
        return [];
      case 5:
        return [];
      case 6:
        return ['maintenancePlan'];
      default:
        return [];
    }
  };

  const handleFormSubmit = (data: VehicleFormData) => {
    onSubmit(data);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="w-full max-w-full space-y-4 sm:space-y-6">
            <div className="w-full max-w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Registration Number *</label>
                <Controller
                  name="registrationNumber"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      placeholder="e.g., GP 123 ABC"
                      disabled={isLoading}
                      className={cn(errors.registrationNumber && "border-red-500")}
                    />
                  )}
                />
                {errors.registrationNumber && (
                  <p className="text-sm text-red-500">{errors.registrationNumber.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Make *</label>
                <Controller
                  name="make"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      placeholder="e.g., Toyota"
                      disabled={isLoading}
                      className={cn(errors.make && "border-red-500")}
                    />
                  )}
                />
                {errors.make && (
                  <p className="text-sm text-red-500">{errors.make.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Model *</label>
                <Controller
                  name="model"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      placeholder="e.g., Hilux"
                      disabled={isLoading}
                      className={cn(errors.model && "border-red-500")}
                    />
                  )}
                />
                {errors.model && (
                  <p className="text-sm text-red-500">{errors.model.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Year *</label>
                <Controller
                  name="year"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="number"
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      disabled={isLoading}
                      className={cn(errors.year && "border-red-500")}
                    />
                  )}
                />
                {errors.year && (
                  <p className="text-sm text-red-500">{errors.year.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">VIN *</label>
                <Controller
                  name="vin"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      placeholder="17-character VIN"
                      maxLength={17}
                      disabled={isLoading}
                      className={cn(errors.vin && "border-red-500")}
                    />
                  )}
                />
                {errors.vin && (
                  <p className="text-sm text-red-500">{errors.vin.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Engine Number *</label>
                <Controller
                  name="engineNumber"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      disabled={isLoading}
                      className={cn(errors.engineNumber && "border-red-500")}
                    />
                  )}
                />
                {errors.engineNumber && (
                  <p className="text-sm text-red-500">{errors.engineNumber.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Fuel Type *</label>
                <Controller
                  name="fuelType"
                  control={control}
                  render={({ field }) => (
                    <select
                      {...field}
                      disabled={isLoading}
                      className={cn(
                        "flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm",
                        errors.fuelType && "border-red-500"
                      )}
                    >
                      <option value="petrol">Petrol</option>
                      <option value="diesel">Diesel</option>
                      <option value="electric">Electric</option>
                      <option value="hybrid">Hybrid</option>
                    </select>
                  )}
                />
                {errors.fuelType && (
                  <p className="text-sm text-red-500">{errors.fuelType.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Category *</label>
                <Controller
                  name="category"
                  control={control}
                  render={({ field }) => (
                    <select
                      {...field}
                      disabled={isLoading}
                      className={cn(
                        "flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm",
                        errors.category && "border-red-500"
                      )}
                    >
                      <option value="sedan">Sedan</option>
                      <option value="suv">SUV</option>
                      <option value="truck">Truck</option>
                      <option value="van">Van</option>
                      <option value="motorcycle">Motorcycle</option>
                      <option value="bus">Bus</option>
                    </select>
                  )}
                />
                {errors.category && (
                  <p className="text-sm text-red-500">{errors.category.message}</p>
                )}
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="w-full max-w-full space-y-6">
            <div className="w-full max-w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Vehicle Status *</label>
                <Controller
                  name="status"
                  control={control}
                  render={({ field }) => (
                    <select
                      {...field}
                      disabled={isLoading}
                      className={cn(
                        "flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm",
                        errors.status && "border-red-500"
                      )}
                    >
                      <option value="active">Active</option>
                      <option value="maintenance">Under Maintenance</option>
                      <option value="retired">Retired</option>
                      <option value="accident">Accident</option>
                    </select>
                  )}
                />
                {errors.status && (
                  <p className="text-sm text-red-500">{errors.status.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Date of Registration *</label>
                <Controller
                  name="dateOfRegistration"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="date"
                      disabled={isLoading}
                      className={cn(errors.dateOfRegistration && "border-red-500")}
                    />
                  )}
                />
                {errors.dateOfRegistration && (
                  <p className="text-sm text-red-500">{errors.dateOfRegistration.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Vehicle Color *</label>
                <Controller
                  name="color"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      placeholder="e.g., White, Blue, Red"
                      disabled={isLoading}
                      className={cn(errors.color && "border-red-500")}
                    />
                  )}
                />
                {errors.color && (
                  <p className="text-sm text-red-500">{errors.color.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Current Odometer Reading (km) *</label>
                <Controller
                  name="odometerReading"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="number"
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      disabled={isLoading}
                      className={cn(errors.odometerReading && "border-red-500")}
                    />
                  )}
                />
                {errors.odometerReading && (
                  <p className="text-sm text-red-500">{errors.odometerReading.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Insurance Expiry *</label>
                <Controller
                  name="insuranceExpiry"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="date"
                      disabled={isLoading}
                      className={cn(errors.insuranceExpiry && "border-red-500")}
                    />
                  )}
                />
                {errors.insuranceExpiry && (
                  <p className="text-sm text-red-500">{errors.insuranceExpiry.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">License Expiry *</label>
                <Controller
                  name="licenseExpiry"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="date"
                      disabled={isLoading}
                      className={cn(errors.licenseExpiry && "border-red-500")}
                    />
                  )}
                />
                {errors.licenseExpiry && (
                  <p className="text-sm text-red-500">{errors.licenseExpiry.message}</p>
                )}
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="w-full max-w-full space-y-6">
            <div className="w-full max-w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Owner/Assigned Department *</label>
                <Controller
                  name="department"
                  control={control}
                  render={({ field }) => (
                    <select
                      {...field}
                      disabled={isLoading}
                      className={cn(
                        "flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm",
                        errors.department && "border-red-500"
                      )}
                    >
                      <option value="">Select Department</option>
                      <option value="Transport">Transport Department</option>
                      <option value="Health">Health Department</option>
                      <option value="Education">Education Department</option>
                      <option value="Public Works">Public Works</option>
                      <option value="Emergency Services">Emergency Services</option>
                    </select>
                  )}
                />
                {errors.department && (
                  <p className="text-sm text-red-500">{errors.department.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Assigned Driver (Optional)</label>
                <Controller
                  name="assignedDriver"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      placeholder="Driver name or ID"
                      disabled={isLoading}
                    />
                  )}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Current Location *</label>
                <Controller
                  name="location"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      placeholder="e.g., Main Depot, Regional Office"
                      disabled={isLoading}
                      className={cn(errors.location && "border-red-500")}
                    />
                  )}
                />
                {errors.location && (
                  <p className="text-sm text-red-500">{errors.location.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Purchase Date *</label>
                <Controller
                  name="purchaseDate"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="date"
                      disabled={isLoading}
                      className={cn(errors.purchaseDate && "border-red-500")}
                    />
                  )}
                />
                {errors.purchaseDate && (
                  <p className="text-sm text-red-500">{errors.purchaseDate.message}</p>
                )}
              </div>

              <div className="space-y-2 lg:col-span-2">
                <label className="text-sm font-medium">Purchase Price (R) *</label>
                <Controller
                  name="purchasePrice"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="number"
                      step="0.01"
                      onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      disabled={isLoading}
                      className={cn(errors.purchasePrice && "border-red-500")}
                    />
                  )}
                />
                {errors.purchasePrice && (
                  <p className="text-sm text-red-500">{errors.purchasePrice.message}</p>
                )}
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Service History</h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => appendService({
                  date: '',
                  type: '',
                  mileage: 0,
                  description: '',
                  cost: 0,
                  serviceProvider: '',
                })}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Service Record
              </Button>
            </div>

            {serviceFields.map((field, index) => (
              <Card key={field.id} className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium">Service Record #{index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeService(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Service Date</label>
                    <Controller
                      name={`serviceHistory.${index}.date`}
                      control={control}
                      render={({ field }) => (
                        <Input {...field} type="date" disabled={isLoading} />
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Service Type</label>
                    <Controller
                      name={`serviceHistory.${index}.type`}
                      control={control}
                      render={({ field }) => (
                        <select
                          {...field}
                          disabled={isLoading}
                          className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm"
                        >
                          <option value="">Select Type</option>
                          <option value="routine_maintenance">Routine Maintenance</option>
                          <option value="repair">Repair</option>
                          <option value="inspection">Inspection</option>
                          <option value="oil_change">Oil Change</option>
                          <option value="tire_replacement">Tire Replacement</option>
                          <option value="brake_service">Brake Service</option>
                        </select>
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Mileage at Service</label>
                    <Controller
                      name={`serviceHistory.${index}.mileage`}
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="number"
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          disabled={isLoading}
                        />
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Cost (R)</label>
                    <Controller
                      name={`serviceHistory.${index}.cost`}
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="number"
                          step="0.01"
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          disabled={isLoading}
                        />
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Service Provider</label>
                    <Controller
                      name={`serviceHistory.${index}.serviceProvider`}
                      control={control}
                      render={({ field }) => (
                        <Input {...field} disabled={isLoading} />
                      )}
                    />
                  </div>

                  <div className="space-y-2 md:col-span-2">
                    <label className="text-sm font-medium">Description</label>
                    <Controller
                      name={`serviceHistory.${index}.description`}
                      control={control}
                      render={({ field }) => (
                        <Textarea {...field} disabled={isLoading} />
                      )}
                    />
                  </div>
                </div>
              </Card>
            ))}
          </div>
        );

      case 5:
        return (
          <div className="space-y-8">
            {/* Parts Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Parts & Serial Numbers</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => appendPart({
                    partName: '',
                    serialNumber: '',
                    manufacturer: '',
                    installationDate: '',
                    partType: 'other',
                  })}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Part
                </Button>
              </div>

              {partFields.map((field, index) => (
                <Card key={field.id} className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-medium">Part #{index + 1}</h4>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removePart(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Part Name</label>
                      <Controller
                        name={`parts.${index}.partName`}
                        control={control}
                        render={({ field }) => (
                          <Input {...field} disabled={isLoading} />
                        )}
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Serial Number</label>
                      <Controller
                        name={`parts.${index}.serialNumber`}
                        control={control}
                        render={({ field }) => (
                          <Input {...field} disabled={isLoading} />
                        )}
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Manufacturer</label>
                      <Controller
                        name={`parts.${index}.manufacturer`}
                        control={control}
                        render={({ field }) => (
                          <Input {...field} disabled={isLoading} />
                        )}
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Installation Date</label>
                      <Controller
                        name={`parts.${index}.installationDate`}
                        control={control}
                        render={({ field }) => (
                          <Input {...field} type="date" disabled={isLoading} />
                        )}
                      />
                    </div>

                    <div className="space-y-2 md:col-span-2">
                      <label className="text-sm font-medium">Part Type</label>
                      <Controller
                        name={`parts.${index}.partType`}
                        control={control}
                        render={({ field }) => (
                          <select
                            {...field}
                            disabled={isLoading}
                            className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm"
                          >
                            <option value="battery">Battery</option>
                            <option value="engine">Engine</option>
                            <option value="transmission">Transmission</option>
                            <option value="brakes">Brakes</option>
                            <option value="tires">Tires</option>
                            <option value="electronics">Electronics</option>
                            <option value="other">Other</option>
                          </select>
                        )}
                      />
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* Warranties Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Part Warranties</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => appendWarranty({
                    partName: '',
                    warrantyProvider: '',
                    startDate: '',
                    endDate: '',
                    warrantyType: 'manufacturer',
                    coverageDetails: '',
                  })}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Warranty
                </Button>
              </div>

              {warrantyFields.map((field, index) => (
                <Card key={field.id} className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-medium">Warranty #{index + 1}</h4>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeWarranty(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Part Name</label>
                      <Controller
                        name={`warranties.${index}.partName`}
                        control={control}
                        render={({ field }) => (
                          <Input {...field} disabled={isLoading} />
                        )}
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Warranty Provider</label>
                      <Controller
                        name={`warranties.${index}.warrantyProvider`}
                        control={control}
                        render={({ field }) => (
                          <Input {...field} disabled={isLoading} />
                        )}
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Start Date</label>
                      <Controller
                        name={`warranties.${index}.startDate`}
                        control={control}
                        render={({ field }) => (
                          <Input {...field} type="date" disabled={isLoading} />
                        )}
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">End Date</label>
                      <Controller
                        name={`warranties.${index}.endDate`}
                        control={control}
                        render={({ field }) => (
                          <Input {...field} type="date" disabled={isLoading} />
                        )}
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Warranty Type</label>
                      <Controller
                        name={`warranties.${index}.warrantyType`}
                        control={control}
                        render={({ field }) => (
                          <select
                            {...field}
                            disabled={isLoading}
                            className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm"
                          >
                            <option value="manufacturer">Manufacturer</option>
                            <option value="extended">Extended</option>
                            <option value="service_provider">Service Provider</option>
                          </select>
                        )}
                      />
                    </div>

                    <div className="space-y-2 md:col-span-2">
                      <label className="text-sm font-medium">Coverage Details</label>
                      <Controller
                        name={`warranties.${index}.coverageDetails`}
                        control={control}
                        render={({ field }) => (
                          <Textarea {...field} disabled={isLoading} />
                        )}
                      />
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        );

      case 6:
        return (
          <div className="w-full max-w-full space-y-6">
            <div className="w-full max-w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Plan Type *</label>
                <Controller
                  name="maintenancePlan.planType"
                  control={control}
                  render={({ field }) => (
                    <select
                      {...field}
                      disabled={isLoading}
                      className={cn(
                        "flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm",
                        errors.maintenancePlan?.planType && "border-red-500"
                      )}
                    >
                      <option value="basic">Basic</option>
                      <option value="comprehensive">Comprehensive</option>
                      <option value="premium">Premium</option>
                      <option value="custom">Custom</option>
                    </select>
                  )}
                />
                {errors.maintenancePlan?.planType && (
                  <p className="text-sm text-red-500">{errors.maintenancePlan.planType.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Service Interval *</label>
                <Controller
                  name="maintenancePlan.serviceInterval"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="number"
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      disabled={isLoading}
                      className={cn(errors.maintenancePlan?.serviceInterval && "border-red-500")}
                    />
                  )}
                />
                {errors.maintenancePlan?.serviceInterval && (
                  <p className="text-sm text-red-500">{errors.maintenancePlan.serviceInterval.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Interval Type *</label>
                <Controller
                  name="maintenancePlan.intervalType"
                  control={control}
                  render={({ field }) => (
                    <select
                      {...field}
                      disabled={isLoading}
                      className={cn(
                        "flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm",
                        errors.maintenancePlan?.intervalType && "border-red-500"
                      )}
                    >
                      <option value="kilometers">Kilometers</option>
                      <option value="months">Months</option>
                    </select>
                  )}
                />
                {errors.maintenancePlan?.intervalType && (
                  <p className="text-sm text-red-500">{errors.maintenancePlan.intervalType.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Next Service Date *</label>
                <Controller
                  name="maintenancePlan.nextServiceDate"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="date"
                      disabled={isLoading}
                      className={cn(errors.maintenancePlan?.nextServiceDate && "border-red-500")}
                    />
                  )}
                />
                {errors.maintenancePlan?.nextServiceDate && (
                  <p className="text-sm text-red-500">{errors.maintenancePlan.nextServiceDate.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Next Service Mileage *</label>
                <Controller
                  name="maintenancePlan.nextServiceMileage"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="number"
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      disabled={isLoading}
                      className={cn(errors.maintenancePlan?.nextServiceMileage && "border-red-500")}
                    />
                  )}
                />
                {errors.maintenancePlan?.nextServiceMileage && (
                  <p className="text-sm text-red-500">{errors.maintenancePlan.nextServiceMileage.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Service Provider *</label>
                <Controller
                  name="maintenancePlan.serviceProvider"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      placeholder="e.g., Mienew, AutoZone"
                      disabled={isLoading}
                      className={cn(errors.maintenancePlan?.serviceProvider && "border-red-500")}
                    />
                  )}
                />
                {errors.maintenancePlan?.serviceProvider && (
                  <p className="text-sm text-red-500">{errors.maintenancePlan.serviceProvider.message}</p>
                )}
              </div>

              <div className="space-y-2 lg:col-span-3">
                <label className="text-sm font-medium">Estimated Cost (R) *</label>
                <Controller
                  name="maintenancePlan.estimatedCost"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="number"
                      step="0.01"
                      onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      disabled={isLoading}
                      className={cn(errors.maintenancePlan?.estimatedCost && "border-red-500")}
                    />
                  )}
                />
                {errors.maintenancePlan?.estimatedCost && (
                  <p className="text-sm text-red-500">{errors.maintenancePlan.estimatedCost.message}</p>
                )}
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="w-full max-w-full space-y-4 sm:space-y-6">
      {/* Progress Bar */}
      <div className="w-full space-y-2">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between w-full gap-1">
          <h2 className="text-base sm:text-lg font-semibold">Vehicle Registration Progress</h2>
          <span className="text-xs sm:text-sm text-gray-600">
            Step {currentStep} of {STEPS.length}
          </span>
        </div>
        <Progress value={progress} className="h-2 w-full" />
      </div>

      {/* Step Navigation */}
      <div className="w-full grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-1 sm:gap-2">
        {STEPS.map((step) => {
          const Icon = step.icon;
          return (
            <div
              key={step.id}
              className={cn(
                "flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-2 rounded-lg text-xs sm:text-sm",
                currentStep === step.id
                  ? "bg-blue-100 text-blue-700"
                  : currentStep > step.id
                  ? "bg-green-100 text-green-700"
                  : "bg-gray-100 text-gray-500"
              )}
            >
              <Icon className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
              <span className="hidden sm:block text-xs lg:text-sm truncate">{step.title}</span>
            </div>
          );
        })}
      </div>

      {/* Form Content */}
      <form onSubmit={handleSubmit(handleFormSubmit)} className="w-full max-w-full space-y-4 sm:space-y-6">
        <Card className="w-full max-w-full">
          <CardHeader className="w-full p-4 sm:p-6">
            <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
              {React.createElement(STEPS[currentStep - 1].icon, { className: "h-4 w-4 sm:h-5 sm:w-5" })}
              {STEPS[currentStep - 1].title}
            </CardTitle>
          </CardHeader>
          <CardContent className="w-full max-w-full p-4 sm:p-6">
            <div className="w-full max-w-full">
              {renderStepContent()}
            </div>
          </CardContent>
        </Card>

        {/* Navigation Buttons */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between w-full max-w-full gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 1}
            className="order-2 sm:order-1"
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Previous</span>
            <span className="sm:hidden">Prev</span>
          </Button>

          <div className="flex items-center gap-2 sm:gap-3 order-1 sm:order-2">
            {onCancel && (
              <Button type="button" variant="ghost" onClick={onCancel} className="flex-1 sm:flex-none">
                Cancel
              </Button>
            )}

            {currentStep < STEPS.length ? (
              <Button type="button" onClick={nextStep} className="flex-1 sm:flex-none">
                Next
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <Button type="submit" disabled={isLoading} className="flex-1 sm:flex-none">
                {isLoading ? 'Saving...' : submitLabel}
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};






