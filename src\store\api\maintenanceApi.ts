import { api } from '../api';
import type { PaginatedResponse } from '../../types/api';

export interface MaintenanceRequest {
  id: string;
  vehicle_id: string;
  vehicle: {
    id: string;
    registration_number: string;
    make: string;
    model: string;
  };
  title: string;
  description: string;
  category: 'routine' | 'repair' | 'emergency' | 'accident' | 'inspection';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'approved' | 'quoted' | 'in_progress' | 'completed' | 'rejected';
  requested_by: string;
  assigned_vendor?: string;
  estimated_cost?: number;
  actual_cost?: number;
  due_date?: string;
  completed_date?: string;
  mileage_at_request: number;
  quotations: Quotation[];
  created_at: string;
  updated_at: string;
}

export interface Quotation {
  id: string;
  vendor_id: string;
  vendor_name: string;
  total_amount: number;
  labor_cost: number;
  parts_cost: number;
  estimated_completion: string;
  line_items: QuotationLineItem[];
  status: 'pending' | 'approved' | 'rejected';
  valid_until: string;
  notes?: string;
  created_at: string;
}

export interface QuotationLineItem {
  id: string;
  description: string;
  category: 'labor' | 'parts' | 'other';
  quantity: number;
  unit_price: number;
  total_price: number;
  benchmark_price?: number;
  variance_percentage?: number;
}

export interface AccidentCase {
  id: string;
  vehicle_id: string;
  driver_id?: string;
  incident_date: string;
  location: string;
  description: string;
  severity: 'minor' | 'moderate' | 'major' | 'total_loss';
  status: 'reported' | 'assessed' | 'approved' | 'in_repair' | 'completed' | 'closed';
  estimated_cost?: number;
  insurance_claim_number?: string;
  police_case_number?: string;
  photos: string[];
  assessor_notes?: string;
  created_at: string;
  updated_at: string;
}

export interface ServiceSchedule {
  id: string;
  vehicle_id: string;
  service_type: 'routine' | 'major' | 'annual' | 'roadworthy';
  scheduled_date: string;
  mileage_due: number;
  status: 'scheduled' | 'overdue' | 'completed' | 'cancelled';
  vendor_id?: string;
  estimated_cost?: number;
  notes?: string;
  created_at: string;
}

export const maintenanceApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Maintenance Requests
    getMaintenanceRequests: builder.query<PaginatedResponse<MaintenanceRequest>, {
      page?: number;
      limit?: number;
      status?: string;
      priority?: string;
      category?: string;
      vehicle_id?: string;
      date_from?: string;
      date_to?: string;
    }>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) params.append(key, String(value));
        });
        return {
          url: '/maintenance/requests',
          params: Object.fromEntries(params),
        };
      },
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: 'WorkOrder' as const, id })),
              { type: 'WorkOrder', id: 'LIST' },
            ]
          : [{ type: 'WorkOrder', id: 'LIST' }],
    }),

    // Get single maintenance request
    getMaintenanceRequest: builder.query<MaintenanceRequest, string>({
      query: (id) => `/maintenance/requests/${id}`,
      providesTags: (result, error, id) => [{ type: 'WorkOrder', id }],
    }),

    // Create maintenance request
    createMaintenanceRequest: builder.mutation<MaintenanceRequest, {
      vehicle_id: string;
      title: string;
      description: string;
      category: string;
      priority: string;
      due_date?: string;
      mileage_at_request: number;
    }>({
      query: (data) => ({
        url: '/maintenance/requests',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'WorkOrder', id: 'LIST' }],
    }),

    // Approve maintenance request
    approveMaintenanceRequest: builder.mutation<MaintenanceRequest, { id: string; notes?: string }>({
      query: ({ id, notes }) => ({
        url: `/maintenance/requests/${id}/approve`,
        method: 'POST',
        body: { notes },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'WorkOrder', id },
        { type: 'WorkOrder', id: 'LIST' },
      ],
    }),

    // Request quotations
    requestQuotations: builder.mutation<void, { request_id: string; vendor_ids: string[] }>({
      query: ({ request_id, vendor_ids }) => ({
        url: `/maintenance/requests/${request_id}/quotations`,
        method: 'POST',
        body: { vendor_ids },
      }),
      invalidatesTags: (result, error, { request_id }) => [
        { type: 'WorkOrder', id: request_id },
      ],
    }),

    // Compare quotations
    compareQuotations: builder.query<{
      quotations: Quotation[];
      benchmark_analysis: {
        lowest_quote: number;
        highest_quote: number;
        average_quote: number;
        recommended_vendor: string;
      };
    }, string>({
      query: (request_id) => `/maintenance/requests/${request_id}/quotations/compare`,
      providesTags: (result, error, request_id) => [
        { type: 'WorkOrder', id: `${request_id}_QUOTATIONS` },
      ],
    }),

    // Approve quotation
    approveQuotation: builder.mutation<MaintenanceRequest, { request_id: string; quotation_id: string }>({
      query: ({ request_id, quotation_id }) => ({
        url: `/maintenance/requests/${request_id}/quotations/${quotation_id}/approve`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { request_id }) => [
        { type: 'WorkOrder', id: request_id },
        { type: 'WorkOrder', id: 'LIST' },
      ],
    }),

    // Accident Cases
    getAccidentCases: builder.query<PaginatedResponse<AccidentCase>, {
      page?: number;
      limit?: number;
      status?: string;
      severity?: string;
      date_from?: string;
      date_to?: string;
    }>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) params.append(key, String(value));
        });
        return {
          url: '/maintenance/accidents',
          params: Object.fromEntries(params),
        };
      },
      providesTags: [{ type: 'WorkOrder', id: 'ACCIDENTS' }],
    }),

    // Report accident
    reportAccident: builder.mutation<AccidentCase, {
      vehicle_id: string;
      driver_id?: string;
      incident_date: string;
      location: string;
      description: string;
      severity: string;
      photos?: File[];
      police_case_number?: string;
    }>({
      query: (data) => ({
        url: '/maintenance/accidents',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'WorkOrder', id: 'ACCIDENTS' }],
    }),

    // Service Scheduling
    getServiceSchedule: builder.query<PaginatedResponse<ServiceSchedule>, {
      page?: number;
      limit?: number;
      vehicle_id?: string;
      status?: string;
      overdue_only?: boolean;
    }>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined) params.append(key, String(value));
        });
        return {
          url: '/maintenance/schedule',
          params: Object.fromEntries(params),
        };
      },
      providesTags: [{ type: 'WorkOrder', id: 'SCHEDULE' }],
    }),

    // Schedule service
    scheduleService: builder.mutation<ServiceSchedule, {
      vehicle_id: string;
      service_type: string;
      scheduled_date: string;
      mileage_due: number;
      vendor_id?: string;
      notes?: string;
    }>({
      query: (data) => ({
        url: '/maintenance/schedule',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'WorkOrder', id: 'SCHEDULE' }],
    }),
  }),
});

export const {
  useGetMaintenanceRequestsQuery,
  useGetMaintenanceRequestQuery,
  useCreateMaintenanceRequestMutation,
  useApproveMaintenanceRequestMutation,
  useRequestQuotationsMutation,
  useCompareQuotationsQuery,
  useApproveQuotationMutation,
  useGetAccidentCasesQuery,
  useReportAccidentMutation,
  useGetServiceScheduleQuery,
  useScheduleServiceMutation,
} = maintenanceApi;