
import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Edit, Settings, Trash2 } from 'lucide-react';
import { useGetVehicleQuery, useDeleteVehicleMutation } from '@/store/api/vehicleApi';
import toast from 'react-hot-toast';
import DeleteConfirmModal from '@/components/modals/DeleteConfirmModal';

export const VehicleDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const {
    data: vehicle,
    isLoading,
  } = useGetVehicleQuery(id!, {
    skip: !id
  });

  const [deleteVehicle, { isLoading: isDeleting }] = useDeleteVehicleMutation();
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDelete = async () => {
    if (!id || !vehicle) return;
    
    try {
      await deleteVehicle(id).unwrap();
      toast.success('Vehicle deleted successfully');
      navigate('/vehicles');
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'data' in error && 
          error.data && typeof error.data === 'object' && 'message' in error.data &&
          typeof error.data.message === 'string') {
        toast.error(error.data.message);
      } else {
        toast.error('Failed to delete vehicle');
      }
    }
  };

  if (isLoading) {
    return <div className="container mx-auto px-4 py-6">Loading vehicle...</div>;
  }

  if (!vehicle) {
    return <div className="container mx-auto px-4 py-6">Vehicle not found</div>;
  }

  return (
    <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6 gap-4">
        <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/vehicles')}
            className="self-start"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Back to Vehicles</span>
            <span className="sm:hidden">Back</span>
          </Button>
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">
              {vehicle.registration_number}
            </h1>
            <p className="text-sm sm:text-base text-gray-600">
              {vehicle.make} {vehicle.model} ({vehicle.year})
            </p>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate(`/vehicles/${id}/edit`)}
            className="h-10 sm:h-auto text-sm"
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate(`/vehicles/${id}/schedule-service`)}
            className="h-10 sm:h-auto text-sm"
          >
            <Settings className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Schedule Service</span>
            <span className="sm:hidden">Service</span>
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={handleDeleteClick}
            disabled={isDeleting}
            className="h-10 sm:h-auto text-sm"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Vehicle Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Status</label>
              <div>
                <Badge variant={vehicle.status === 'active' ? 'default' : 'secondary'}>
                  {vehicle.status}
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Department</label>
              <p className="text-gray-900">{vehicle.department}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">VIN</label>
              <p className="text-gray-900">{vehicle.vin}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Engine Number</label>
              <p className="text-gray-900">{vehicle.engine_number}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Assigned Driver</label>
              <p className="text-gray-900">{vehicle.assigned_driver?.name || 'Unassigned'}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Delete confirmation modal */}
      <DeleteConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDelete}
        itemName={vehicle.registration_number}
        itemType="Vehicle"
        isLoading={isDeleting}
      />
    </div>
  );
};















