import React, { useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Edit, 
  Download, 
  MessageSquare, 
  Clock, 
  CheckCircle,
  Car,
  User,
  DollarSign,
  FileText,
  Camera,
  Phone,
  Mail,
  Eye,
  ThumbsUp,
  ThumbsDown,
  Send,
  Paperclip,
  MoreVertical,
  Flag,
  Archive,
  Trash2
} from 'lucide-react';

interface WorkOrder {
  id: string;
  workOrderNumber: string;
  title: string;
  description: string;
  status: 'Draft' | 'Pending Approval' | 'Approved' | 'In Progress' | 'Completed' | 'Cancelled';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  vehicle: {
    id: string;
    registrationNumber: string;
    make: string;
    model: string;
    year: number;
    department: string;
    currentMileage: number;
  };
  requestedBy: {
    name: string;
    department: string;
    email: string;
    phone: string;
  };
  assignedVendor?: {
    id: string;
    name: string;
    contactPerson: string;
    email: string;
    phone: string;
    rating: number;
  };
  serviceType: string;
  estimatedCost: number;
  actualCost?: number;
  estimatedDuration: string;
  scheduledDate: string;
  completedDate?: string;
  createdDate: string;
  updatedDate: string;
  attachments: Array<{
    id: string;
    name: string;
    type: string;
    size: number;
    uploadedBy: string;
    uploadedDate: string;
  }>;
  quotes: Array<{
    id: string;
    vendorId: string;
    vendorName: string;
    amount: number;
    description: string;
    validUntil: string;
    status: 'Pending' | 'Approved' | 'Rejected';
    submittedDate: string;
  }>;
  timeline: Array<{
    id: string;
    action: string;
    description: string;
    performedBy: string;
    timestamp: string;
    type: 'status_change' | 'comment' | 'attachment' | 'quote' | 'approval';
  }>;
}

const WorkOrderDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');
  const [newComment, setNewComment] = useState('');
  const [showActions, setShowActions] = useState(false);

  // Mock data - replace with API call
  const workOrder: WorkOrder = {
    id: id || '1',
    workOrderNumber: 'WO-2024-001',
    title: 'Brake Pad Replacement',
    description: 'Vehicle requires immediate brake pad replacement due to excessive wear. Driver reported squealing noise and reduced braking efficiency.',
    status: 'Pending Approval',
    priority: 'High',
    vehicle: {
      id: '1',
      registrationNumber: 'GP 123 ABC',
      make: 'Toyota',
      model: 'Hilux',
      year: 2022,
      department: 'Transport',
      currentMileage: 45000
    },
    requestedBy: {
      name: 'John Smith',
      department: 'Transport',
      email: '<EMAIL>',
      phone: '+27 11 123 4567'
    },
    assignedVendor: {
      id: 'v1',
      name: 'AutoZone Service Center',
      contactPerson: 'Mike Johnson',
      email: '<EMAIL>',
      phone: '+27 11 987 6543',
      rating: 4.5
    },
    serviceType: 'Brake Service',
    estimatedCost: 2500,
    estimatedDuration: '4 hours',
    scheduledDate: '2024-01-20T09:00:00Z',
    createdDate: '2024-01-15T10:30:00Z',
    updatedDate: '2024-01-15T14:20:00Z',
    attachments: [
      {
        id: '1',
        name: 'brake_inspection_report.pdf',
        type: 'application/pdf',
        size: 245760,
        uploadedBy: 'John Smith',
        uploadedDate: '2024-01-15T10:35:00Z'
      },
      {
        id: '2',
        name: 'brake_pad_photo.jpg',
        type: 'image/jpeg',
        size: 1024000,
        uploadedBy: 'John Smith',
        uploadedDate: '2024-01-15T10:36:00Z'
      }
    ],
    quotes: [
      {
        id: '1',
        vendorId: 'v1',
        vendorName: 'AutoZone Service Center',
        amount: 2500,
        description: 'Front brake pad replacement including labor and parts',
        validUntil: '2024-01-25T23:59:59Z',
        status: 'Pending',
        submittedDate: '2024-01-16T09:00:00Z'
      },
      {
        id: '2',
        vendorId: 'v2',
        vendorName: 'Quick Brake Pro',
        amount: 2200,
        description: 'Complete brake pad replacement with premium pads',
        validUntil: '2024-01-24T23:59:59Z',
        status: 'Pending',
        submittedDate: '2024-01-16T11:30:00Z'
      }
    ],
    timeline: [
      {
        id: '1',
        action: 'Work Order Created',
        description: 'Work order created by John Smith',
        performedBy: 'John Smith',
        timestamp: '2024-01-15T10:30:00Z',
        type: 'status_change'
      },
      {
        id: '2',
        action: 'Documents Uploaded',
        description: 'Brake inspection report and photos uploaded',
        performedBy: 'John Smith',
        timestamp: '2024-01-15T10:35:00Z',
        type: 'attachment'
      },
      {
        id: '3',
        action: 'Quote Received',
        description: 'Quote received from AutoZone Service Center - R2,500',
        performedBy: 'System',
        timestamp: '2024-01-16T09:00:00Z',
        type: 'quote'
      },
      {
        id: '4',
        action: 'Quote Received',
        description: 'Quote received from Quick Brake Pro - R2,200',
        performedBy: 'System',
        timestamp: '2024-01-16T11:30:00Z',
        type: 'quote'
      }
    ]
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Draft': return 'bg-gray-100 text-gray-800';
      case 'Pending Approval': return 'bg-yellow-100 text-yellow-800';
      case 'Approved': return 'bg-blue-100 text-blue-800';
      case 'In Progress': return 'bg-purple-100 text-purple-800';
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'Cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'High': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'Medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTimelineIcon = (type: string) => {
    switch (type) {
      case 'status_change': return <Clock className="h-4 w-4 text-blue-600" />;
      case 'comment': return <MessageSquare className="h-4 w-4 text-green-600" />;
      case 'attachment': return <Paperclip className="h-4 w-4 text-purple-600" />;
      case 'quote': return <DollarSign className="h-4 w-4 text-orange-600" />;
      case 'approval': return <CheckCircle className="h-4 w-4 text-green-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const handleApprove = () => {
    // Handle approval logic
    console.log('Approving work order');
  };

  const handleReject = () => {
    // Handle rejection logic
    console.log('Rejecting work order');
  };

  const handleAddComment = () => {
    if (newComment.trim()) {
      // Add comment logic
      console.log('Adding comment:', newComment);
      setNewComment('');
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: FileText },
    { id: 'quotes', label: 'Quotes', icon: DollarSign },
    { id: 'timeline', label: 'Timeline', icon: Clock },
    { id: 'attachments', label: 'Attachments', icon: Paperclip }
  ];

  return (
    <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8 space-y-4 sm:space-y-6 max-w-4xl">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center space-x-3 sm:space-x-4">
          <button
            onClick={() => navigate('/work-orders')}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors flex-shrink-0"
          >
            <ArrowLeft className="h-4 w-4 sm:h-5 sm:w-5" />
          </button>
          <div className="min-w-0">
            <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 truncate">{workOrder.workOrderNumber}</h1>
            <p className="text-sm sm:text-base text-gray-600 truncate">{workOrder.title}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2 sm:space-x-3">
          <div className="relative">
            <button
              onClick={() => setShowActions(!showActions)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <MoreVertical className="h-4 w-4 sm:h-5 sm:w-5" />
            </button>
            {showActions && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                <div className="py-1">
                  <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2">
                    <Flag className="h-4 w-4" />
                    <span>Flag for Review</span>
                  </button>
                  <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2">
                    <Archive className="h-4 w-4" />
                    <span>Archive</span>
                  </button>
                  <button className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2">
                    <Trash2 className="h-4 w-4" />
                    <span>Delete</span>
                  </button>
                </div>
              </div>
            )}
          </div>
          <button
            onClick={() => navigate(`/work-orders/${id}/edit`)}
            className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 sm:px-4 py-2 rounded-lg flex items-center space-x-1 sm:space-x-2 transition-colors text-sm sm:text-base"
          >
            <Edit className="h-4 w-4" />
            <span className="hidden sm:inline">Edit</span>
          </button>
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-3 sm:px-4 py-2 rounded-lg flex items-center space-x-1 sm:space-x-2 transition-colors text-sm sm:text-base">
            <Download className="h-4 w-4" />
            <span className="hidden sm:inline">Export</span>
          </button>
        </div>
      </div>

      {/* Status and Priority */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(workOrder.status)}`}>
              {workOrder.status}
            </span>
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getPriorityColor(workOrder.priority)}`}>
              {workOrder.priority} Priority
            </span>
          </div>
          {workOrder.status === 'Pending Approval' && (
            <div className="flex space-x-2">
              <button 
                onClick={handleReject}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
              >
                <ThumbsDown className="h-4 w-4" />
                <span>Reject</span>
              </button>
              <button 
                onClick={handleApprove}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
              >
                <ThumbsUp className="h-4 w-4" />
                <span>Approve</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Key Information Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Car className="h-5 w-5 text-blue-600" />
            <h3 className="font-semibold text-gray-900">Vehicle</h3>
          </div>
          <div className="space-y-2">
            <p className="font-medium text-gray-900">{workOrder.vehicle.registrationNumber}</p>
            <p className="text-sm text-gray-600">{workOrder.vehicle.make} {workOrder.vehicle.model} ({workOrder.vehicle.year})</p>
            <p className="text-sm text-gray-600">{workOrder.vehicle.department} Department</p>
            <p className="text-sm text-gray-600">{workOrder.vehicle.currentMileage.toLocaleString()} km</p>
          </div>
          <button 
            onClick={() => navigate(`/vehicles/${workOrder.vehicle.id}`)}
            className="mt-4 text-blue-600 hover:text-blue-800 text-sm flex items-center space-x-1"
          >
            <Eye className="h-4 w-4" />
            <span>View Vehicle</span>
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <User className="h-5 w-5 text-green-600" />
            <h3 className="font-semibold text-gray-900">Requested By</h3>
          </div>
          <div className="space-y-2">
            <p className="font-medium text-gray-900">{workOrder.requestedBy.name}</p>
            <p className="text-sm text-gray-600">{workOrder.requestedBy.department} Department</p>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Mail className="h-3 w-3" />
              <span>{workOrder.requestedBy.email}</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Phone className="h-3 w-3" />
              <span>{workOrder.requestedBy.phone}</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <DollarSign className="h-5 w-5 text-orange-600" />
            <h3 className="font-semibold text-gray-900">Cost Information</h3>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Estimated Cost:</span>
              <span className="font-medium text-gray-900">{formatCurrency(workOrder.estimatedCost)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Duration:</span>
              <span className="font-medium text-gray-900">{workOrder.estimatedDuration}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Scheduled:</span>
              <span className="font-medium text-gray-900">{new Date(workOrder.scheduledDate).toLocaleDateString()}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    isActive
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                  {tab.id === 'quotes' && (
                    <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                      {workOrder.quotes.length}
                    </span>
                  )}
                  {tab.id === 'attachments' && (
                    <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                      {workOrder.attachments.length}
                    </span>
                  )}
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Description</h4>
                <p className="text-gray-700">{workOrder.description}</p>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Service Type</h4>
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                  {workOrder.serviceType}
                </span>
              </div>

              {workOrder.assignedVendor && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Assigned Vendor</h4>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900">{workOrder.assignedVendor.name}</p>
                        <p className="text-sm text-gray-600">Contact: {workOrder.assignedVendor.contactPerson}</p>
                        <div className="flex items-center space-x-4 mt-2">
                          <div className="flex items-center space-x-1 text-sm text-gray-600">
                            <Mail className="h-3 w-3" />
                            <span>{workOrder.assignedVendor.email}</span>
                          </div>
                          <div className="flex items-center space-x-1 text-sm text-gray-600">
                            <Phone className="h-3 w-3" />
                            <span>{workOrder.assignedVendor.phone}</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center space-x-1">
                          <span className="text-sm text-gray-600">Rating:</span>
                          <span className="font-medium text-yellow-600">{workOrder.assignedVendor.rating}/5</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'quotes' && (
            <div className="space-y-4">
              {workOrder.quotes.map((quote) => (
                <div key={quote.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h5 className="font-medium text-gray-900">{quote.vendorName}</h5>
                      <p className="text-sm text-gray-600">Submitted {formatTimeAgo(quote.submittedDate)}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-gray-900">{formatCurrency(quote.amount)}</p>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        quote.status === 'Approved' ? 'bg-green-100 text-green-800' :
                        quote.status === 'Rejected' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {quote.status}
                      </span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-700 mb-3">{quote.description}</p>
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>Valid until: {new Date(quote.validUntil).toLocaleDateString()}</span>
                    {quote.status === 'Pending' && (
                      <div className="flex space-x-2">
                        <button className="text-red-600 hover:text-red-800">Reject</button>
                        <button className="text-green-600 hover:text-green-800">Approve</button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'timeline' && (
            <div className="space-y-4">
              {workOrder.timeline.map((event) => (
                <div key={event.id} className="flex items-start space-x-3">
                  <div className="p-2 bg-gray-100 rounded-full">
                    {getTimelineIcon(event.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h5 className="font-medium text-gray-900">{event.action}</h5>
                      <span className="text-sm text-gray-500">{formatTimeAgo(event.timestamp)}</span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{event.description}</p>
                    <p className="text-xs text-gray-500 mt-1">by {event.performedBy}</p>
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'attachments' && (
            <div className="space-y-4">
              {workOrder.attachments.map((attachment) => (
                <div key={attachment.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded">
                      <FileText className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{attachment.name}</p>
                      <p className="text-sm text-gray-600">
                        {formatFileSize(attachment.size)} • Uploaded by {attachment.uploadedBy} • {formatTimeAgo(attachment.uploadedDate)}
                      </p>
                    </div>
                  </div>
                  <button className="text-blue-600 hover:text-blue-800 flex items-center space-x-1">
                    <Download className="h-4 w-4" />
                    <span>Download</span>
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Comments Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Comments & Updates</h3>
        
        <div className="space-y-4 mb-6">
          {/* Sample comments would go here */}
          <div className="text-center py-8 text-gray-500">
            <MessageSquare className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p>No comments yet. Be the first to add an update.</p>
          </div>
        </div>

        <div className="border-t border-gray-200 pt-4">
          <div className="flex space-x-3">
            <div className="flex-1">
              <textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Add a comment or update..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="flex flex-col space-y-2">
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <Paperclip className="h-5 w-5" />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <Camera className="h-5 w-5" />
              </button>
            </div>
          </div>
          <div className="flex justify-end mt-3">
            <button 
              onClick={handleAddComment}
              disabled={!newComment.trim()}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white px-4 py-2 rounded-lg"
            >
              Add Comment
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkOrderDetailPage;

