import React, { useState } from 'react';
import { 
  BarChart3, 
  Download, 
  Calendar, 
  Settings,
  Plus,
  Play,
  Clock,
  Save,
  FileText
} from 'lucide-react';

// Report Builder interfaces
interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  parameters: ReportParameter[];
  schedule?: ReportSchedule;
}

interface ReportParameter {
  id: string;
  name: string;
  type: 'date' | 'select' | 'multiselect' | 'number' | 'text';
  label: string;
  required: boolean;
  options?: string[];
  defaultValue?: any;
}

interface ReportSchedule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  time: string;
  recipients: string[];
  enabled: boolean;
}

const ReportsPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showReportBuilder, setShowReportBuilder] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const [reportParameters, setReportParameters] = useState<Record<string, any>>({});
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: '',
  });

  // Report templates for the builder
  const reportTemplates: ReportTemplate[] = [
    {
      id: 'custom-fleet-utilization',
      name: 'Custom Fleet Utilization',
      description: 'Build a custom fleet utilization report with flexible parameters',
      category: 'operational',
      parameters: [
        { id: 'dateRange', name: 'dateRange', type: 'date', label: 'Date Range', required: true },
        { id: 'departments', name: 'departments', type: 'multiselect', label: 'Departments', required: false, options: ['Public Works', 'Health', 'Education', 'Transport'] },
        { id: 'vehicleTypes', name: 'vehicleTypes', type: 'multiselect', label: 'Vehicle Types', required: false, options: ['Sedan', 'SUV', 'Truck', 'Van'] },
        { id: 'includeIdleTime', name: 'includeIdleTime', type: 'select', label: 'Include Idle Time', required: false, options: ['Yes', 'No'], defaultValue: 'Yes' }
      ]
    },
    {
      id: 'custom-maintenance-analysis',
      name: 'Custom Maintenance Analysis',
      description: 'Analyze maintenance costs and patterns with custom filters',
      category: 'financial',
      parameters: [
        { id: 'dateRange', name: 'dateRange', type: 'date', label: 'Date Range', required: true },
        { id: 'costThreshold', name: 'costThreshold', type: 'number', label: 'Minimum Cost (R)', required: false },
        { id: 'vendors', name: 'vendors', type: 'multiselect', label: 'Vendors', required: false, options: ['AutoFix Pro', 'Fleet Services SA', 'Quick Repair'] },
        { id: 'maintenanceType', name: 'maintenanceType', type: 'select', label: 'Maintenance Type', required: false, options: ['All', 'Preventive', 'Corrective', 'Emergency'] }
      ]
    }
  ];

  const existingReportCards = [
    {
      id: 'fleet-utilization',
      title: 'Fleet Utilization Report',
      description: 'Vehicle usage statistics and efficiency metrics',
      icon: <BarChart3 className="h-6 w-6 text-blue-600" />,
      category: 'operational',
      lastGenerated: '2024-01-15',
      frequency: 'monthly',
    },
    {
      id: 'maintenance-costs',
      title: 'Maintenance Cost Analysis',
      description: 'Breakdown of maintenance expenses by vehicle and vendor',
      icon: <FileText className="h-6 w-6 text-green-600" />,
      category: 'financial',
      lastGenerated: '2024-01-14',
      frequency: 'monthly',
    }
  ];

  const allReports = [...existingReportCards, ...reportTemplates.map(template => ({
    id: template.id,
    title: template.name,
    description: template.description,
    icon: <Settings className="h-6 w-6 text-indigo-600" />,
    category: template.category,
    lastGenerated: undefined,
    frequency: 'custom' as const,
    isTemplate: true
  }))];

  const filteredReports = allReports.filter(report => {
    const matchesCategory = selectedCategory === 'all' || report.category === selectedCategory;
    const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const categories = [
    { value: 'all', label: 'All Reports', count: allReports.length },
    { value: 'financial', label: 'Financial', count: allReports.filter(r => r.category === 'financial').length },
    { value: 'operational', label: 'Operational', count: allReports.filter(r => r.category === 'operational').length },
  ];

  const handleGenerateReport = (reportId: string) => {
    const template = reportTemplates.find(t => t.id === reportId);
    if (template) {
      setSelectedTemplate(template);
      setShowReportBuilder(true);
    } else {
      console.log('Generating standard report:', reportId, 'with date range:', dateRange);
    }
  };

  const handleBuildCustomReport = () => {
    console.log('Building custom report with parameters:', reportParameters);
    setShowReportBuilder(false);
  };

  const renderParameterInput = (param: ReportParameter) => {
    switch (param.type) {
      case 'date':
        return (
          <div className="grid grid-cols-2 gap-2">
            <input
              type="date"
              placeholder="Start Date"
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              onChange={(e) => setReportParameters(prev => ({
                ...prev,
                [`${param.id}_start`]: e.target.value
              }))}
            />
            <input
              type="date"
              placeholder="End Date"
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              onChange={(e) => setReportParameters(prev => ({
                ...prev,
                [`${param.id}_end`]: e.target.value
              }))}
            />
          </div>
        );
      case 'select':
        return (
          <select
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            onChange={(e) => setReportParameters(prev => ({
              ...prev,
              [param.id]: e.target.value
            }))}
            defaultValue={param.defaultValue}
          >
            <option value="">Select {param.label}</option>
            {param.options?.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        );
      default:
        return (
          <input
            type="text"
            placeholder={param.label}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            onChange={(e) => setReportParameters(prev => ({
              ...prev,
              [param.id]: e.target.value
            }))}
          />
        );
    }
  };

  if (showReportBuilder && selectedTemplate) {
    return (
      <div className="space-y-4 sm:space-y-6 p-3 sm:p-4 lg:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Report Builder</h1>
            <p className="text-sm sm:text-base text-gray-600">Configure and generate: {selectedTemplate.name}</p>
          </div>
          <button
            onClick={() => setShowReportBuilder(false)}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 text-sm sm:text-base h-10 sm:h-auto self-start"
          >
            <span className="hidden sm:inline">Back to Reports</span>
            <span className="sm:hidden">Back</span>
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-4 sm:p-6 border-b border-gray-200">
            <div className="flex items-center space-x-2">
              <Settings className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
              <h3 className="text-base sm:text-lg font-semibold">Report Configuration</h3>
            </div>
            <p className="text-sm sm:text-base text-gray-600 mt-1">{selectedTemplate.description}</p>
          </div>
          <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
            {selectedTemplate.parameters.map(param => (
              <div key={param.id} className="space-y-2">
                <label className="block text-xs sm:text-sm font-medium text-gray-700">
                  {param.label}
                  {param.required && <span className="text-red-500 ml-1">*</span>}
                </label>
                {renderParameterInput(param)}
              </div>
            ))}

            <div className="border-t pt-4 sm:pt-6">
              <h4 className="font-medium text-gray-900 mb-3 text-sm sm:text-base">Output Options</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-2">Format</label>
                  <select className="w-full px-3 py-2 h-10 sm:h-9 border border-gray-300 rounded-lg text-base sm:text-sm">
                    <option>PDF</option>
                    <option>Excel (XLSX)</option>
                    <option>CSV</option>
                  </select>
                </div>
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-2">Email Recipients</label>
                  <input
                    type="text"
                    placeholder="<EMAIL>, <EMAIL>"
                    className="w-full px-3 py-2 h-10 sm:h-9 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 text-base sm:text-sm"
                  />
                </div>
              </div>
            </div>

            <div className="flex space-x-3 pt-4">
              <button 
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center space-x-2"
                onClick={handleBuildCustomReport}
              >
                <Play className="h-4 w-4" />
                <span>Generate Report</span>
              </button>
              <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 flex items-center space-x-2">
                <Save className="h-4 w-4" />
                <span>Save Template</span>
              </button>
              <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 flex items-center space-x-2">
                <Clock className="h-4 w-4" />
                <span>Schedule</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
          <p className="text-gray-600">Generate and manage fleet management reports</p>
        </div>
        <button 
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
          onClick={() => setShowReportBuilder(true)}
        >
          <Plus className="h-4 w-4" />
          <span>Custom Report Builder</span>
        </button>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search reports..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex gap-2">
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
              placeholder="Start Date"
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
              placeholder="End Date"
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Category Tabs */}
      <div className="flex flex-wrap gap-2">
        {categories.map((category) => (
          <button
            key={category.value}
            onClick={() => setSelectedCategory(category.value)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedCategory === category.value 
                ? "bg-blue-600 text-white" 
                : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
            }`}
          >
            {category.label} ({category.count})
          </button>
        ))}
      </div>

      {/* Reports Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredReports.map((report) => (
          <div key={report.id} className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  {report.icon}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{report.title}</h3>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className={`px-2 py-1 rounded-md text-xs font-medium ${
                        report.category === 'financial' ? 'bg-green-100 text-green-800' :
                        report.category === 'operational' ? 'bg-blue-100 text-blue-800' :
                        'bg-purple-100 text-purple-800'
                      }`}>
                        {report.category.charAt(0).toUpperCase() + report.category.slice(1)}
                      </span>
                      {report.frequency && (
                        <span className={`px-2 py-1 rounded-md text-xs font-medium ${
                          report.frequency === 'custom' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {report.frequency === 'custom' ? 'Custom' : report.frequency}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              
              <p className="text-gray-600 text-sm mb-4">{report.description}</p>
              
              {report.lastGenerated && (
                <p className="text-sm text-gray-500 mb-3">
                  Last generated: {report.lastGenerated}
                </p>
              )}
              
              <div className="flex space-x-2">
                <button 
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm flex items-center justify-center space-x-1"
                  onClick={() => handleGenerateReport(report.id)}
                >
                  {(report as any).isTemplate ? (
                    <>
                      <Settings className="h-4 w-4" />
                      <span>Configure</span>
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4" />
                      <span>Generate</span>
                    </>
                  )}
                </button>
                {!(report as any).isTemplate && (
                  <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 text-sm flex items-center space-x-1">
                    <Calendar className="h-4 w-4" />
                    <span>Schedule</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ReportsPage;

