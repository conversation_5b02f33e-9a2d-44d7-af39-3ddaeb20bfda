import { api } from '../api';
import type { PaginatedResponse } from '../../types/api';

export interface Inspection {
  id: string;
  vehicle_id: string;
  vehicle: {
    registration_number: string;
    make: string;
    model: string;
  };
  inspector_id: string;
  inspector_name: string;
  inspection_type: 'roadworthy' | 'annual' | 'pre_trip' | 'post_trip' | 'accident' | 'compliance';
  status: 'scheduled' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  scheduled_date: string;
  completed_date?: string;
  location: string;
  mileage: number;
  overall_result: 'pass' | 'fail' | 'conditional_pass';
  checklist_items: InspectionChecklistItem[];
  photos: InspectionPhoto[];
  notes: string;
  certificate_number?: string;
  next_inspection_due?: string;
  created_at: string;
  updated_at: string;
}

export interface InspectionChecklistItem {
  id: string;
  category: string;
  item: string;
  status: 'pass' | 'fail' | 'advisory' | 'not_applicable';
  notes?: string;
  photo_ids: string[];
  required: boolean;
}

export interface InspectionPhoto {
  id: string;
  url: string;
  thumbnail_url: string;
  description: string;
  checklist_item_id?: string;
  uploaded_at: string;
}

export interface InspectionTemplate {
  id: string;
  name: string;
  type: string;
  categories: InspectionCategory[];
  is_active: boolean;
  created_at: string;
}

export interface InspectionCategory {
  id: string;
  name: string;
  items: InspectionTemplateItem[];
  order: number;
}

export interface InspectionTemplateItem {
  id: string;
  description: string;
  required: boolean;
  photo_required: boolean;
  order: number;
}

export const inspectorApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get inspections
    getInspections: builder.query<PaginatedResponse<Inspection>, {
      page?: number;
      limit?: number;
      status?: string;
      type?: string;
      inspector_id?: string;
      vehicle_id?: string;
      date_from?: string;
      date_to?: string;
    }>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) params.append(key, String(value));
        });
        return {
          url: '/inspections',
          params: Object.fromEntries(params),
        };
      },
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: 'Inspector' as const, id })),
              { type: 'Inspector', id: 'LIST' },
            ]
          : [{ type: 'Inspector', id: 'LIST' }],
    }),

    // Get single inspection
    getInspection: builder.query<Inspection, string>({
      query: (id) => `/inspections/${id}`,
      providesTags: (result, error, id) => [{ type: 'Inspector', id }],
    }),

    // Create inspection
    createInspection: builder.mutation<Inspection, {
      vehicle_id: string;
      inspection_type: string;
      scheduled_date: string;
      location: string;
      template_id: string;
    }>({
      query: (data) => ({
        url: '/inspections',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'Inspector', id: 'LIST' }],
    }),

    // Start inspection
    startInspection: builder.mutation<Inspection, { id: string; mileage: number }>({
      query: ({ id, mileage }) => ({
        url: `/inspections/${id}/start`,
        method: 'POST',
        body: { mileage },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Inspector', id },
        { type: 'Inspector', id: 'LIST' },
      ],
    }),

    // Update checklist item
    updateChecklistItem: builder.mutation<Inspection, {
      inspection_id: string;
      item_id: string;
      status: string;
      notes?: string;
    }>({
      query: ({ inspection_id, item_id, ...data }) => ({
        url: `/inspections/${inspection_id}/checklist/${item_id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { inspection_id }) => [
        { type: 'Inspector', id: inspection_id },
      ],
    }),

    // Upload inspection photo
    uploadInspectionPhoto: builder.mutation<InspectionPhoto, {
      inspection_id: string;
      file: File;
      description: string;
      checklist_item_id?: string;
    }>({
      query: ({ inspection_id, file, description, checklist_item_id }) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('description', description);
        if (checklist_item_id) formData.append('checklist_item_id', checklist_item_id);
        
        return {
          url: `/inspections/${inspection_id}/photos`,
          method: 'POST',
          body: formData,
        };
      },
      invalidatesTags: (result, error, { inspection_id }) => [
        { type: 'Inspector', id: inspection_id },
      ],
    }),

    // Complete inspection
    completeInspection: builder.mutation<Inspection, {
      id: string;
      overall_result: string;
      notes: string;
      next_inspection_due?: string;
    }>({
      query: ({ id, ...data }) => ({
        url: `/inspections/${id}/complete`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Inspector', id },
        { type: 'Inspector', id: 'LIST' },
      ],
    }),

    // Get inspection templates
    getInspectionTemplates: builder.query<InspectionTemplate[], { type?: string }>({
      query: (filters = {}) => ({
        url: '/inspections/templates',
        params: filters,
      }),
      providesTags: [{ type: 'Inspector', id: 'TEMPLATES' }],
    }),

    // Generate inspection report
    generateInspectionReport: builder.mutation<{ download_url: string }, {
      inspection_id: string;
      format: 'pdf' | 'excel';
    }>({
      query: (data) => ({
        url: '/inspections/report',
        method: 'POST',
        body: data,
      }),
    }),

    // Get inspector dashboard stats
    getInspectorStats: builder.query<{
      total_inspections: number;
      pending_inspections: number;
      completed_today: number;
      pass_rate: number;
      overdue_inspections: number;
    }, { inspector_id?: string; date_from?: string; date_to?: string }>({
      query: (filters = {}) => ({
        url: '/inspections/stats',
        params: filters,
      }),
      providesTags: [{ type: 'Inspector', id: 'STATS' }],
    }),
  }),
});

export const {
  useGetInspectionsQuery,
  useGetInspectionQuery,
  useCreateInspectionMutation,
  useStartInspectionMutation,
  useUpdateChecklistItemMutation,
  useUploadInspectionPhotoMutation,
  useCompleteInspectionMutation,
  useGetInspectionTemplatesQuery,
  useGenerateInspectionReportMutation,
  useGetInspectorStatsQuery,
} = inspectorApi;