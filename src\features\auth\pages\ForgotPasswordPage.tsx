import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useNavigate, Link } from 'react-router-dom';
import logoImage from '@/assets/image1.png';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertCircle, Mail, ArrowLeft } from 'lucide-react';
import { Label } from '@radix-ui/react-label';

const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

const ForgotPasswordPage: React.FC = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = React.useState(false);
  const [success, setSuccess] = React.useState<string | null>(null);
  const [error, setError] = React.useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
  });

  const onSubmit = async (data: ForgotPasswordFormData) => {
    try {
      setIsLoading(true);
      setError(null);
      setSuccess(null);

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simulate password reset request
      // console.log('Password reset requested for:', data.email);

      setSuccess('Password reset instructions have been sent to your email address. Please check your inbox and follow the instructions to reset your password.');

      // Auto-redirect to login after 5 seconds
      setTimeout(() => {
        navigate('/login', { 
          state: { 
            message: 'Password reset email sent. Please check your inbox.',
            email: data.email 
          }
        });
      }, 5000);

    } catch (err) {
      setError('An error occurred while sending the reset email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-3 sm:p-4 lg:p-6">
      <div className="w-full max-w-sm sm:max-w-md space-y-4 sm:space-y-6">
        {/* Header */}
        <div className="text-center space-y-3 sm:space-y-4">
          <div className="flex justify-center">
            <img src={logoImage} alt="Fleet Management Logo" className="h-16 sm:h-20 lg:h-24" />
          </div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-800 px-2">RT46-2026 Fleet Management</h1>
          <p className="text-sm sm:text-base text-gray-600 px-2">Reset your password</p>
        </div>

        {/* Forgot Password Form */}
        <Card className="shadow-md">
          <CardHeader className="space-y-3 sm:space-y-4 p-4 sm:p-6">
            <CardTitle className="text-lg sm:text-xl text-center">Forgot Password</CardTitle>
            <CardDescription className="text-center text-sm sm:text-base">
              Enter your email address and we'll send you instructions to reset your password
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 p-4 sm:p-6 pt-0">
            {error && (
              <Alert className="border-red-200 bg-red-50">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-sm">{error}</AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert className="border-green-200 bg-green-50">
                <AlertCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800 text-sm leading-relaxed">{success}</AlertDescription>
              </Alert>
            )}

            {!success && (
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                    Email Address
                  </Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email address"
                      className={`pl-10 h-11 sm:h-10 text-base sm:text-sm ${errors.email ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                      {...register('email')}
                    />
                  </div>
                  {errors.email && (
                    <p className="text-sm text-red-600">{errors.email.message}</p>
                  )}
                </div>

                <Button
                  type="submit"
                  className="w-full h-11 sm:h-10 text-base sm:text-sm"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      <span className="hidden sm:inline">Sending Reset Email...</span>
                      <span className="sm:hidden">Sending...</span>
                    </>
                  ) : (
                    <>
                      <span className="hidden sm:inline">Send Reset Instructions</span>
                      <span className="sm:hidden">Send Instructions</span>
                    </>
                  )}
                </Button>
              </form>
            )}

            {/* Back to Login */}
            <div className="text-center pt-3 sm:pt-4 border-t">
              <Link
                to="/login"
                className="inline-flex items-center gap-2 text-xs sm:text-sm font-medium text-blue-600 hover:text-blue-500 hover:underline"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Sign In
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Security Notice */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-start space-x-3">
              <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">Security Notice</p>
                <p className="leading-relaxed">
                  For security reasons, we'll only send password reset instructions to registered email addresses.
                  If you don't receive an email within a few minutes, please check your spam folder or contact support.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
