import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Save, 
  Send, 
  Car, 
  Wrench, 
  Calendar, 
  DollarSign,
  FileText,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import toast from 'react-hot-toast';

interface WorkOrderFormData {
  // Basic Information
  vehicleId: string;
  title: string;
  description: string;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  category: string;
  workType: 'Scheduled Maintenance' | 'Repair' | 'Inspection' | 'Emergency';
  
  // Assignment
  assignedVendor: string;
  requestedBy: string;
  department: string;
  
  // Scheduling
  preferredDate: string;
  deadline: string;
  estimatedDuration: string;
  
  // Location & Access
  serviceLocation: string;
  specialInstructions: string;
  accessRequirements: string;
  
  // Cost Information
  estimatedCost: string;
  budgetCode: string;
  approvalRequired: boolean;
  
  // Additional Details
  symptoms: string[];
  attachments: File[];
  urgencyReason: string;
}

const STEPS = [
  { id: 1, title: 'Basic Info', icon: Car },
  { id: 2, title: 'Work Details', icon: Wrench },
  { id: 3, title: 'Schedule', icon: Calendar },
  { id: 4, title: 'Cost & Notes', icon: DollarSign },
];

const CreateWorkOrderPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState<WorkOrderFormData>({
    vehicleId: '',
    title: '',
    description: '',
    priority: 'Medium',
    category: '',
    workType: 'Repair',
    assignedVendor: '',
    requestedBy: '',
    department: '',
    preferredDate: '',
    deadline: '',
    estimatedDuration: '',
    serviceLocation: '',
    specialInstructions: '',
    accessRequirements: '',
    estimatedCost: '',
    budgetCode: '',
    approvalRequired: false,
    symptoms: [],
    attachments: [],
    urgencyReason: ''
  });

  const progress = (currentStep / STEPS.length) * 100;

  const updateFormData = (field: keyof WorkOrderFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateStep = (step: number) => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (!formData.vehicleId) newErrors.vehicleId = 'Please select a vehicle';
        if (!formData.title.trim()) newErrors.title = 'Please enter a work order title';
        if (!formData.description.trim()) newErrors.description = 'Please provide a description';
        break;
      case 2:
        if (!formData.category) newErrors.category = 'Please select a category';
        if (!formData.assignedVendor) newErrors.assignedVendor = 'Please select a vendor';
        if (!formData.department) newErrors.department = 'Please select a department';
        break;
      case 3:
        if (!formData.preferredDate) newErrors.preferredDate = 'Please select a preferred date';
        if (!formData.serviceLocation) newErrors.serviceLocation = 'Please specify service location';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, STEPS.length));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async (action: 'draft' | 'submit') => {
    if (action === 'submit') {
      // Validate all steps
      let hasErrors = false;
      for (let step = 1; step <= STEPS.length; step++) {
        if (!validateStep(step)) {
          hasErrors = true;
          setCurrentStep(step); // Navigate to first step with errors
          break;
        }
      }
      if (hasErrors) return;
    }

    setIsSubmitting(true);
    try {
      console.log('Creating work order:', { ...formData, status: action === 'draft' ? 'Draft' : 'Pending' });
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success(`Work order ${action === 'draft' ? 'saved as draft' : 'submitted'} successfully`);
      navigate('/work-orders');
    } catch (error) {
      console.error('Error creating work order:', error);
      toast.error('Failed to create work order');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="w-full space-y-6">
            <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Vehicle *
                </label>
                <select
                  value={formData.vehicleId}
                  onChange={(e) => updateFormData('vehicleId', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.vehicleId ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">Select a vehicle</option>
                  <option value="1">GP-123-ABC - Toyota Hilux</option>
                  <option value="2">GP-456-DEF - Ford Ranger</option>
                  <option value="3">GP-789-GHI - Nissan NP200</option>
                </select>
                {errors.vehicleId && (
                  <p className="mt-1 text-sm text-red-600">{errors.vehicleId}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Priority
                </label>
                <select
                  value={formData.priority}
                  onChange={(e) => updateFormData('priority', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="Low">Low</option>
                  <option value="Medium">Medium</option>
                  <option value="High">High</option>
                  <option value="Critical">Critical</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Work Order Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => updateFormData('title', e.target.value)}
                placeholder="e.g., Brake pad replacement required"
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.title ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.title && (
                <p className="mt-1 text-sm text-red-600">{errors.title}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => updateFormData('description', e.target.value)}
                placeholder="Provide detailed description of the work required..."
                rows={4}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.description ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description}</p>
              )}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="w-full space-y-6">
            <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Work Type
                </label>
                <select
                  value={formData.workType}
                  onChange={(e) => updateFormData('workType', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="Scheduled Maintenance">Scheduled Maintenance</option>
                  <option value="Repair">Repair</option>
                  <option value="Inspection">Inspection</option>
                  <option value="Emergency">Emergency</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category *
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => updateFormData('category', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.category ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">Select category</option>
                  <option value="Engine">Engine</option>
                  <option value="Brakes">Brakes</option>
                  <option value="Transmission">Transmission</option>
                  <option value="Electrical">Electrical</option>
                  <option value="Body Work">Body Work</option>
                </select>
                {errors.category && (
                  <p className="mt-1 text-sm text-red-600">{errors.category}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Assigned Vendor *
                </label>
                <select
                  value={formData.assignedVendor}
                  onChange={(e) => updateFormData('assignedVendor', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.assignedVendor ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">Select vendor</option>
                  <option value="AutoFix Pro">AutoFix Pro</option>
                  <option value="Fleet Services SA">Fleet Services SA</option>
                  <option value="Quick Repair">Quick Repair</option>
                </select>
                {errors.assignedVendor && (
                  <p className="mt-1 text-sm text-red-600">{errors.assignedVendor}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Department *
                </label>
                <select
                  value={formData.department}
                  onChange={(e) => updateFormData('department', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.department ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">Select department</option>
                  <option value="Health">Health</option>
                  <option value="Education">Education</option>
                  <option value="Transport">Transport</option>
                  <option value="Public Works">Public Works</option>
                </select>
                {errors.department && (
                  <p className="mt-1 text-sm text-red-600">{errors.department}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Requested By
              </label>
              <input
                type="text"
                value={formData.requestedBy}
                onChange={(e) => updateFormData('requestedBy', e.target.value)}
                placeholder="Enter requester name"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        );

      case 3:
        return (
          <div className="w-full space-y-6">
            <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Preferred Date *
                </label>
                <input
                  type="date"
                  value={formData.preferredDate}
                  onChange={(e) => updateFormData('preferredDate', e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.preferredDate ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.preferredDate && (
                  <p className="mt-1 text-sm text-red-600">{errors.preferredDate}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Deadline
                </label>
                <input
                  type="date"
                  value={formData.deadline}
                  onChange={(e) => updateFormData('deadline', e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Estimated Duration
                </label>
                <select
                  value={formData.estimatedDuration}
                  onChange={(e) => updateFormData('estimatedDuration', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select duration</option>
                  <option value="1 hour">1 hour</option>
                  <option value="2 hours">2 hours</option>
                  <option value="Half day">Half day</option>
                  <option value="Full day">Full day</option>
                  <option value="2-3 days">2-3 days</option>
                  <option value="1 week">1 week</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Service Location *
                </label>
                <input
                  type="text"
                  value={formData.serviceLocation}
                  onChange={(e) => updateFormData('serviceLocation', e.target.value)}
                  placeholder="e.g., Main garage, On-site, Vendor location"
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.serviceLocation ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.serviceLocation && (
                  <p className="mt-1 text-sm text-red-600">{errors.serviceLocation}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Special Instructions
              </label>
              <textarea
                value={formData.specialInstructions}
                onChange={(e) => updateFormData('specialInstructions', e.target.value)}
                placeholder="Any special requirements, access instructions, or notes for the vendor..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        );

      case 4:
        return (
          <div className="w-full space-y-6">
            <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Estimated Cost (ZAR)
                </label>
                <input
                  type="number"
                  value={formData.estimatedCost}
                  onChange={(e) => updateFormData('estimatedCost', e.target.value)}
                  placeholder="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Budget Code
                </label>
                <input
                  type="text"
                  value={formData.budgetCode}
                  onChange={(e) => updateFormData('budgetCode', e.target.value)}
                  placeholder="e.g., MAINT-2024-001"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="approvalRequired"
                checked={formData.approvalRequired}
                onChange={(e) => updateFormData('approvalRequired', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="approvalRequired" className="text-sm text-gray-700">
                Requires management approval
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Access Requirements
              </label>
              <textarea
                value={formData.accessRequirements}
                onChange={(e) => updateFormData('accessRequirements', e.target.value)}
                placeholder="Special access requirements, security clearances, etc..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {formData.priority === 'Critical' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Urgency Reason
                </label>
                <textarea
                  value={formData.urgencyReason}
                  onChange={(e) => updateFormData('urgencyReason', e.target.value)}
                  placeholder="Explain why this work order is critical..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-6">
      {/* Header */}
      <div className="mb-4 sm:mb-6">
        <Button
          variant="ghost"
          onClick={() => navigate('/work-orders')}
          className="mb-3 sm:mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span className="hidden sm:inline">Back to Work Orders</span>
          <span className="sm:hidden">Back</span>
        </Button>

        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Create Work Order</h1>
          <p className="text-sm sm:text-base text-gray-600">Submit a new maintenance or repair request</p>
        </div>
      </div>

      {/* Form Card */}
      <Card className="w-full">
        <CardHeader className="p-4 sm:p-6">
          <CardTitle className="text-lg sm:text-xl">Work Order Creation</CardTitle>
        </CardHeader>
        <CardContent className="w-full p-4 sm:p-6">
          <div className="w-full space-y-4 sm:space-y-6">
            {/* Progress Bar */}
            <div className="w-full space-y-2">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between w-full gap-1">
                <h2 className="text-base sm:text-lg font-semibold">Work Order Progress</h2>
                <span className="text-xs sm:text-sm text-gray-600">
                  Step {currentStep} of {STEPS.length}
                </span>
              </div>
              <Progress value={progress} className="h-2 w-full" />
            </div>

            {/* Step Navigation */}
            <div className="w-full grid grid-cols-2 sm:grid-cols-4 gap-1 sm:gap-2">
              {STEPS.map((step) => {
                const Icon = step.icon;
                return (
                  <div
                    key={step.id}
                    className={cn(
                      "flex items-center space-x-2 px-3 py-2 rounded-lg text-sm",
                      currentStep === step.id
                        ? "bg-blue-100 text-blue-700"
                        : currentStep > step.id
                        ? "bg-green-100 text-green-700"
                        : "bg-gray-100 text-gray-500"
                    )}
                  >
                    <Icon className="h-4 w-4" />
                    <span className="hidden sm:block text-xs lg:text-sm">{step.title}</span>
                  </div>
                );
              })}
            </div>

            {/* Form Content */}
            <form className="w-full space-y-6">
              <Card className="w-full">
                <CardHeader className="w-full">
                  <CardTitle className="flex items-center gap-2">
                    {React.createElement(STEPS[currentStep - 1].icon, { className: "h-5 w-5" })}
                    {STEPS[currentStep - 1].title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="w-full p-6">
                  <div className="w-full">
                    {renderStepContent()}
                  </div>
                </CardContent>
              </Card>

              {/* Navigation Buttons */}
              <div className="flex items-center justify-between w-full">
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  disabled={currentStep === 1}
                >
                  <ChevronLeft className="h-4 w-4 mr-2" />
                  Previous
                </Button>

                <div className="flex items-center gap-3">
                  <Button 
                    type="button" 
                    variant="ghost" 
                    onClick={() => navigate('/work-orders')}
                  >
                    Cancel
                  </Button>

                  <Button 
                    type="button"
                    variant="outline"
                    onClick={() => handleSubmit('draft')}
                    disabled={isSubmitting}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save Draft
                  </Button>

                  {currentStep < STEPS.length ? (
                    <Button type="button" onClick={nextStep}>
                      Next
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </Button>
                  ) : (
                    <Button 
                      type="button" 
                      onClick={() => handleSubmit('submit')}
                      disabled={isSubmitting}
                    >
                      <Send className="h-4 w-4 mr-2" />
                      {isSubmitting ? 'Submitting...' : 'Submit'}
                    </Button>
                  )}
                </div>
              </div>
            </form>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CreateWorkOrderPage;


