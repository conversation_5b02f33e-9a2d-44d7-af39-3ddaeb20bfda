import React, { useState } from 'react';
import { Card, CardContent} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  FileText, 
  Download, 
  Eye, 
  Clock,
  CheckCircle,
  AlertTriangle,
  DollarSign,
  Calendar,
  Search,
  Filter,
  Phone
} from 'lucide-react';

interface Invoice {
  id: string;
  invoiceNumber: string;
  jobCardId: string;
  workOrderId: string;
  submittedDate: string;
  dueDate: string;
  amount: number;
  status: 'Submitted' | 'Under Review' | 'Approved' | 'Paid' | 'Rejected' | 'Overdue';
  paymentDate?: string;
  customer: {
    department: string;
    contactPerson: string;
    phone: string;
    email: string;
  };
  vehicle: {
    registration: string;
    make: string;
    model: string;
  };
  serviceType: string;
  paymentReference?: string;
  rejectionReason?: string;
  reviewNotes?: string;
}

const PaymentStatusTracker: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');

  // Mock data - replace with API call
  const invoices: Invoice[] = [
    {
      id: '1',
      invoiceNumber: 'INV-2025-001',
      jobCardId: 'JC-2025-001',
      workOrderId: 'WO-2025-001',
      submittedDate: '2025-01-15T10:30:00Z',
      dueDate: '2025-02-14T23:59:59Z',
      amount: 1978.50,
      status: 'Paid',
      paymentDate: '2025-01-28T14:20:00Z',
      paymentReference: 'PAY-2025-001',
      customer: {
        department: 'Department of Health',
        contactPerson: 'Dr. Sarah Johnson',
        phone: '************',
        email: '<EMAIL>'
      },
      vehicle: {
        registration: 'GP123ABC',
        make: 'Toyota',
        model: 'Hilux'
      },
      serviceType: 'Brake Service'
    },
    {
      id: '2',
      invoiceNumber: 'INV-2025-002',
      jobCardId: 'JC-2025-002',
      workOrderId: 'WO-2025-002',
      submittedDate: '2025-01-20T09:15:00Z',
      dueDate: '2025-02-19T23:59:59Z',
      amount: 3250.75,
      status: 'Approved',
      customer: {
        department: 'Department of Education',
        contactPerson: 'Mr. John Smith',
        phone: '************',
        email: '<EMAIL>'
      },
      vehicle: {
        registration: 'GP456DEF',
        make: 'Ford',
        model: 'Ranger'
      },
      serviceType: 'Engine Service'
    },
    {
      id: '3',
      invoiceNumber: 'INV-2025-003',
      jobCardId: 'JC-2025-003',
      workOrderId: 'WO-2025-003',
      submittedDate: '2025-01-22T11:45:00Z',
      dueDate: '2025-02-21T23:59:59Z',
      amount: 850.00,
      status: 'Under Review',
      reviewNotes: 'Additional documentation requested for parts verification',
      customer: {
        department: 'Department of Transport',
        contactPerson: 'Ms. Lisa Brown',
        phone: '************',
        email: '<EMAIL>'
      },
      vehicle: {
        registration: 'GP789GHI',
        make: 'Isuzu',
        model: 'KB'
      },
      serviceType: 'Tire Replacement'
    },
    {
      id: '4',
      invoiceNumber: 'INV-2025-004',
      jobCardId: 'JC-2025-004',
      workOrderId: 'WO-2025-004',
      submittedDate: '2025-01-10T16:20:00Z',
      dueDate: '2025-02-09T23:59:59Z',
      amount: 1200.00,
      status: 'Overdue',
      customer: {
        department: 'Department of Agriculture',
        contactPerson: 'Dr. Michael Wilson',
        phone: '************',
        email: '<EMAIL>'
      },
      vehicle: {
        registration: 'GP321JKL',
        make: 'Nissan',
        model: 'Hardbody'
      },
      serviceType: 'Transmission Service'
    },
    {
      id: '5',
      invoiceNumber: 'INV-2025-005',
      jobCardId: 'JC-2025-005',
      workOrderId: 'WO-2025-005',
      submittedDate: '2025-01-25T13:10:00Z',
      dueDate: '2025-02-24T23:59:59Z',
      amount: 450.00,
      status: 'Rejected',
      rejectionReason: 'Incomplete supporting documentation - missing parts receipts',
      customer: {
        department: 'Department of Water Affairs',
        contactPerson: 'Mr. David Lee',
        phone: '************',
        email: '<EMAIL>'
      },
      vehicle: {
        registration: 'GP654MNO',
        make: 'Toyota',
        model: 'Quantum'
      },
      serviceType: 'Oil Change'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Paid': return 'bg-green-100 text-green-800';
      case 'Approved': return 'bg-blue-100 text-blue-800';
      case 'Under Review': return 'bg-yellow-100 text-yellow-800';
      case 'Submitted': return 'bg-gray-100 text-gray-800';
      case 'Rejected': return 'bg-red-100 text-red-800';
      case 'Overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Paid': return <CheckCircle className="h-4 w-4" />;
      case 'Approved': return <CheckCircle className="h-4 w-4" />;
      case 'Under Review': return <Clock className="h-4 w-4" />;
      case 'Submitted': return <FileText className="h-4 w-4" />;
      case 'Rejected': return <AlertTriangle className="h-4 w-4" />;
      case 'Overdue': return <AlertTriangle className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = 
      invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.customer.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.vehicle.registration.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter;
    
    const matchesDate = dateFilter === 'all' || (() => {
      const invoiceDate = new Date(invoice.submittedDate);
      const now = new Date();
      const daysDiff = Math.floor((now.getTime() - invoiceDate.getTime()) / (1000 * 60 * 60 * 24));
      
      switch (dateFilter) {
        case 'last7': return daysDiff <= 7;
        case 'last30': return daysDiff <= 30;
        case 'last90': return daysDiff <= 90;
        default: return true;
      }
    })();
    
    return matchesSearch && matchesStatus && matchesDate;
  });

  const calculateSummary = () => {
    const total = invoices.reduce((sum, inv) => sum + inv.amount, 0);
    const paid = invoices.filter(inv => inv.status === 'Paid').reduce((sum, inv) => sum + inv.amount, 0);
    const pending = invoices.filter(inv => ['Submitted', 'Under Review', 'Approved'].includes(inv.status)).reduce((sum, inv) => sum + inv.amount, 0);
    const overdue = invoices.filter(inv => inv.status === 'Overdue').reduce((sum, inv) => sum + inv.amount, 0);
    
    return { total, paid, pending, overdue };
  };

  const { total, paid, pending, overdue } = calculateSummary();

  const handleViewInvoice = (invoiceId: string) => {
    console.log('Viewing invoice:', invoiceId);
  };

  const handleDownloadInvoice = (invoiceId: string) => {
    console.log('Downloading invoice:', invoiceId);
  };

  const handleContactCustomer = (invoice: Invoice) => {
    console.log('Contacting customer:', invoice.customer);
  };

  return (
    <div className="container mx-auto px-6 py-8 space-y-6 max-w-7xl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payment Status</h1>
          <p className="text-gray-600">Track invoice payments and financial status</p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Invoiced</p>
                <p className="text-2xl font-bold text-gray-900">R{total.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Paid</p>
                <p className="text-2xl font-bold text-green-600">R{paid.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">R{pending.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <AlertTriangle className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Overdue</p>
                <p className="text-2xl font-bold text-red-600">R{overdue.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by invoice number, department, or vehicle..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="Submitted">Submitted</SelectItem>
                  <SelectItem value="Under Review">Under Review</SelectItem>
                  <SelectItem value="Approved">Approved</SelectItem>
                  <SelectItem value="Paid">Paid</SelectItem>
                  <SelectItem value="Rejected">Rejected</SelectItem>
                  <SelectItem value="Overdue">Overdue</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={dateFilter} onValueChange={setDateFilter}>
                <SelectTrigger className="w-32">
                  <Calendar className="h-4 w-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="last7">Last 7 days</SelectItem>
                  <SelectItem value="last30">Last 30 days</SelectItem>
                  <SelectItem value="last90">Last 90 days</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Invoices List */}
      <div className="space-y-4">
        {filteredInvoices.map((invoice) => (
          <Card key={invoice.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-4">
                    <div>
                      <div className="flex items-center space-x-2">
                        <h3 className="text-lg font-semibold">{invoice.invoiceNumber}</h3>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                          {getStatusIcon(invoice.status)}
                          <span className="ml-1">{invoice.status}</span>
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">
                        Job Card: {invoice.jobCardId} • {invoice.serviceType}
                      </p>
                    </div>
                  </div>
                  
                  <div className="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Customer</p>
                      <p className="text-sm">{invoice.customer.department}</p>
                      <p className="text-xs text-gray-500">{invoice.customer.contactPerson}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Vehicle</p>
                      <p className="text-sm">{invoice.vehicle.make} {invoice.vehicle.model}</p>
                      <p className="text-xs text-gray-500">{invoice.vehicle.registration}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Amount</p>
                      <p className="text-lg font-bold">R{invoice.amount.toLocaleString()}</p>
                      <p className="text-xs text-gray-500">
                        Due: {new Date(invoice.dueDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Submitted</p>
                      <p className="text-sm">{new Date(invoice.submittedDate).toLocaleDateString()}</p>
                      {invoice.paymentDate && (
                        <p className="text-xs text-green-600">
                          Paid: {new Date(invoice.paymentDate).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Status-specific information */}
                  {invoice.status === 'Rejected' && invoice.rejectionReason && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded">
                      <p className="text-sm text-red-800">
                        <strong>Rejection Reason:</strong> {invoice.rejectionReason}
                      </p>
                    </div>
                  )}

                  {invoice.status === 'Under Review' && invoice.reviewNotes && (
                    <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                      <p className="text-sm text-yellow-800">
                        <strong>Review Notes:</strong> {invoice.reviewNotes}
                      </p>
                    </div>
                  )}

                  {invoice.status === 'Paid' && invoice.paymentReference && (
                    <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded">
                      <p className="text-sm text-green-800">
                        <strong>Payment Reference:</strong> {invoice.paymentReference}
                      </p>
                    </div>
                  )}

                  {invoice.status === 'Overdue' && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded">
                      <p className="text-sm text-red-800">
                        <strong>Overdue:</strong> Payment was due on {new Date(invoice.dueDate).toLocaleDateString()}
                      </p>
                    </div>
                  )}
                </div>

                <div className="flex flex-col space-y-2 ml-6">
                  <Button size="sm" variant="outline" onClick={() => handleViewInvoice(invoice.id)}>
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => handleDownloadInvoice(invoice.id)}>
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                  {['Under Review', 'Overdue'].includes(invoice.status) && (
                    <Button size="sm" variant="outline" onClick={() => handleContactCustomer(invoice)}>
                      <Phone className="h-4 w-4 mr-2" />
                      Contact
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredInvoices.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No invoices found</h3>
            <p className="text-gray-600">
              {searchTerm || statusFilter !== 'all' || dateFilter !== 'all'
                ? 'Try adjusting your search criteria or filters.'
                : 'You haven\'t submitted any invoices yet.'}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PaymentStatusTracker;
