import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Download,
  Send,
  Clock,
  CheckCircle,
  XCircle,
  DollarSign,
  Calendar,
  FileText
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface Invoice {
  id: string;
  invoiceNumber: string;
  jobCardId: string;
  workOrderId: string;
  status: 'Draft' | 'Submitted' | 'Under Review' | 'Approved' | 'Paid' | 'Rejected';
  amount: number;
  createdDate: string;
  submittedDate?: string;
  dueDate?: string;
  customer: {
    department: string;
    contactPerson: string;
  };
  vehicle: {
    registration: string;
    make: string;
    model: string;
  };
  serviceType: string;
}

const InvoiceManagement: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Mock data - replace with API call
  const invoices: Invoice[] = [
    {
      id: '1',
      invoiceNumber: 'INV-2025-001',
      jobCardId: 'JC-2025-001',
      workOrderId: 'WO-2025-001',
      status: 'Paid',
      amount: 1978.50,
      createdDate: '2025-01-15T10:30:00Z',
      submittedDate: '2025-01-15T11:00:00Z',
      dueDate: '2025-02-14T23:59:59Z',
      customer: {
        department: 'Department of Health',
        contactPerson: 'Dr. Sarah Johnson'
      },
      vehicle: {
        registration: 'GP123ABC',
        make: 'Toyota',
        model: 'Hilux'
      },
      serviceType: 'Brake Service'
    },
    {
      id: '2',
      invoiceNumber: 'INV-2025-002',
      jobCardId: 'JC-2025-002',
      workOrderId: 'WO-2025-002',
      status: 'Under Review',
      amount: 3250.75,
      createdDate: '2025-01-20T09:15:00Z',
      submittedDate: '2025-01-20T10:00:00Z',
      dueDate: '2025-02-19T23:59:59Z',
      customer: {
        department: 'Department of Education',
        contactPerson: 'Mr. John Smith'
      },
      vehicle: {
        registration: 'GP456DEF',
        make: 'Ford',
        model: 'Ranger'
      },
      serviceType: 'Engine Service'
    },
    {
      id: '3',
      invoiceNumber: 'DRAFT-001',
      jobCardId: 'JC-2025-003',
      workOrderId: 'WO-2025-003',
      status: 'Draft',
      amount: 850.00,
      createdDate: '2025-01-22T11:45:00Z',
      customer: {
        department: 'Department of Transport',
        contactPerson: 'Ms. Lisa Brown'
      },
      vehicle: {
        registration: 'GP789GHI',
        make: 'Isuzu',
        model: 'KB'
      },
      serviceType: 'Tire Replacement'
    },
    {
      id: '4',
      invoiceNumber: 'INV-2025-004',
      jobCardId: 'JC-2025-004',
      workOrderId: 'WO-2025-004',
      status: 'Approved',
      amount: 1200.00,
      createdDate: '2025-01-10T16:20:00Z',
      submittedDate: '2025-01-11T09:00:00Z',
      dueDate: '2025-02-09T23:59:59Z',
      customer: {
        department: 'Department of Agriculture',
        contactPerson: 'Dr. Michael Wilson'
      },
      vehicle: {
        registration: 'GP321JKL',
        make: 'Nissan',
        model: 'Hardbody'
      },
      serviceType: 'Transmission Service'
    },
    {
      id: '5',
      invoiceNumber: 'INV-2025-005',
      jobCardId: 'JC-2025-005',
      workOrderId: 'WO-2025-005',
      status: 'Rejected',
      amount: 450.00,
      createdDate: '2025-01-25T13:10:00Z',
      submittedDate: '2025-01-25T14:00:00Z',
      customer: {
        department: 'Department of Water Affairs',
        contactPerson: 'Mr. David Lee'
      },
      vehicle: {
        registration: 'GP654MNO',
        make: 'Toyota',
        model: 'Quantum'
      },
      serviceType: 'Oil Change'
    }
  ];

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = 
      invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.customer.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.vehicle.registration.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Draft': return 'bg-gray-100 text-gray-800';
      case 'Submitted': return 'bg-blue-100 text-blue-800';
      case 'Under Review': return 'bg-yellow-100 text-yellow-800';
      case 'Approved': return 'bg-green-100 text-green-800';
      case 'Paid': return 'bg-green-100 text-green-800';
      case 'Rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Draft': return <Edit className="h-3 w-3" />;
      case 'Submitted': return <Send className="h-3 w-3" />;
      case 'Under Review': return <Clock className="h-3 w-3" />;
      case 'Approved': return <CheckCircle className="h-3 w-3" />;
      case 'Paid': return <DollarSign className="h-3 w-3" />;
      case 'Rejected': return <XCircle className="h-3 w-3" />;
      default: return <FileText className="h-3 w-3" />;
    }
  };

  const calculateSummary = () => {
    const draft = invoices.filter(inv => inv.status === 'Draft').length;
    const submitted = invoices.filter(inv => inv.status === 'Submitted').length;
    const underReview = invoices.filter(inv => inv.status === 'Under Review').length;
    const approved = invoices.filter(inv => inv.status === 'Approved').length;
    const paid = invoices.filter(inv => inv.status === 'Paid').length;
    const rejected = invoices.filter(inv => inv.status === 'Rejected').length;
    
    return { draft, submitted, underReview, approved, paid, rejected };
  };

  const { draft, submitted, underReview, approved, paid, rejected } = calculateSummary();

  const handleCreateInvoice = () => {
    navigate('/merchant/invoices/create');
  };

  const handleEditInvoice = (invoiceId: string) => {
    navigate(`/merchant/invoices/edit/${invoiceId}`);
  };

  const handleViewInvoice = (invoiceId: string) => {
    navigate(`/merchant/invoices/view/${invoiceId}`);
  };

  const handleSubmitInvoice = (invoiceId: string) => {
    console.log('Submitting invoice:', invoiceId);
    // API call to submit invoice
  };

  return (
    <div className="container mx-auto px-6 py-8 space-y-6 max-w-7xl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Invoice Management</h1>
          <p className="text-gray-600">Create and manage invoices for completed work</p>
        </div>
        <Button onClick={() => navigate('/merchant/invoices/create')}>
          <Plus className="h-4 w-4 mr-2" />
          Create Invoice
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-600">{draft}</p>
              <p className="text-sm text-gray-500">Draft</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{submitted}</p>
              <p className="text-sm text-gray-500">Submitted</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600">{underReview}</p>
              <p className="text-sm text-gray-500">Under Review</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{approved}</p>
              <p className="text-sm text-gray-500">Approved</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{paid}</p>
              <p className="text-sm text-gray-500">Paid</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">{rejected}</p>
              <p className="text-sm text-gray-500">Rejected</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search invoices..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="Draft">Draft</SelectItem>
                <SelectItem value="Submitted">Submitted</SelectItem>
                <SelectItem value="Under Review">Under Review</SelectItem>
                <SelectItem value="Approved">Approved</SelectItem>
                <SelectItem value="Paid">Paid</SelectItem>
                <SelectItem value="Rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Invoices List */}
      <div className="space-y-4">
        {filteredInvoices.map((invoice) => (
          <Card key={invoice.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-4">
                    <div>
                      <div className="flex items-center space-x-2">
                        <h3 className="text-lg font-semibold">{invoice.invoiceNumber}</h3>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                          {getStatusIcon(invoice.status)}
                          <span className="ml-1">{invoice.status}</span>
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">
                        Job Card: {invoice.jobCardId} • {invoice.serviceType}
                      </p>
                    </div>
                  </div>
                  
                  <div className="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Customer</p>
                      <p className="text-sm">{invoice.customer.department}</p>
                      <p className="text-xs text-gray-500">{invoice.customer.contactPerson}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Vehicle</p>
                      <p className="text-sm">{invoice.vehicle.make} {invoice.vehicle.model}</p>
                      <p className="text-xs text-gray-500">{invoice.vehicle.registration}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Amount</p>
                      <p className="text-lg font-bold">R{invoice.amount.toLocaleString()}</p>
                      {invoice.dueDate && (
                        <p className="text-xs text-gray-500">
                          Due: {new Date(invoice.dueDate).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Created</p>
                      <p className="text-sm">{new Date(invoice.createdDate).toLocaleDateString()}</p>
                      {invoice.submittedDate && (
                        <p className="text-xs text-gray-500">
                          Submitted: {new Date(invoice.submittedDate).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex flex-col space-y-2 ml-6">
                  <Button size="sm" variant="outline" onClick={() => handleViewInvoice(invoice.id)}>
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </Button>
                  {invoice.status === 'Draft' && (
                    <>
                      <Button size="sm" variant="outline" onClick={() => handleEditInvoice(invoice.id)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                      <Button size="sm" onClick={() => handleSubmitInvoice(invoice.id)}>
                        <Send className="h-4 w-4 mr-2" />
                        Submit
                      </Button>
                    </>
                  )}
                  <Button size="sm" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredInvoices.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No invoices found</h3>
            <p className="text-gray-600">
              {searchTerm || statusFilter !== 'all'
                ? 'Try adjusting your search criteria or filters.'
                : 'You haven\'t created any invoices yet.'}
            </p>
            <Button className="mt-4" onClick={handleCreateInvoice}>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Invoice
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default InvoiceManagement;
