Item #,<PERSON><PERSON><PERSON>,Google Cloud Services,Open Source / External,,Comments
1,Work Allocation Engine,"Cloud Workflows, Cloud Functions, Cloud Run Jobs",Airflow (on GKE),,
2,Vehicle & Merchant Registry,"Cloud SQL (PostgreSQL), Firestore","(hosted or local), Directus (admin UI)",,
3,Maintenance & Repair Orders,"Custom backend on Cloud Run, API Gateway",Tryton (on GKE),,
4,Inspection App,Cloud Storage,React Native,,ES - PWA and Offline capablity
5,Audit Logging,"Cloud Logging, Pub/Sub, BigQuery",,,
6,Fraud & Anomaly Detection,"Vertex AI, BigQuery ML, AutoML Tables","Wazuh, pyod 2.0.5",,
7,Pricing & Parts Monitoring,"BigQuery, Cloud Scheduler, Vertex AI Forecast",—-,,
8,Analytics & Reporting,"Looker, Data Studio, BigQuery BI Engine",Directus,,
9,Search & Geo Matching,"Vertex AI Matching Engine, Firestore full-text search","Meilisearch, PostGIS",,
10,Document & Image AI,"Document AI, Vision AI",Tesseract,,
11,Authentication & SSO,N/A (bring-your-own),"<PERSON><PERSON><PERSON>lla (OIDC, RBAC, MFA)",,
12,Orchestration & Workflow,"Optional: Cloud Workflows, Scheduler",Conductor,,
13,Secrets Management,Secret Manager,—,,
14,Container Orchestration,"GKE (Kubernetes), Cloud Run",—,,
15,CI/CD & DevOps,Cloud Deploy,GitHub Actions,,
16,Infrastructure as Code,Terraform (preferred),—,,
17,Monitoring & Alerting,"Cloud Monitoring, Error Reporting, Uptime Checks",Prometheus,,
18,Feature Flags / Remote Config,Postgres,,,
19,Mobile Crash & Feedback,"Firebase Crashlytics, Performance Monitoring",—,,
20,API Gateway / Security,Apigee,,,