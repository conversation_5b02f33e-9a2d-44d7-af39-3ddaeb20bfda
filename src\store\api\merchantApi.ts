import { api } from '../api';
import type { PaginatedResponse } from '../../types/api';

export interface MerchantWorkOrder {
  id: string;
  work_order_number: string;
  vehicle: {
    id: string;
    registration_number: string;
    make: string;
    model: string;
    year: number;
  };
  title: string;
  description: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'assigned' | 'quoted' | 'approved' | 'in_progress' | 'completed' | 'invoiced';
  assigned_date: string;
  due_date?: string;
  estimated_cost?: number;
  approved_amount?: number;
  actual_cost?: number;
  completion_date?: string;
  customer_notes?: string;
  merchant_notes?: string;
  photos: string[];
  parts_required: WorkOrderPart[];
  labor_items: WorkOrderLabor[];
}

export interface WorkOrderPart {
  id: string;
  part_number: string;
  description: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  supplier?: string;
}

export interface WorkOrderLabor {
  id: string;
  description: string;
  hours: number;
  hourly_rate: number;
  total_cost: number;
  technician?: string;
}

export interface MerchantQuote {
  id: string;
  work_order_id: string;
  total_amount: number;
  labor_cost: number;
  parts_cost: number;
  other_costs: number;
  estimated_completion_date: string;
  valid_until: string;
  status: 'draft' | 'submitted' | 'approved' | 'rejected' | 'expired';
  line_items: QuoteLineItem[];
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface QuoteLineItem {
  id: string;
  type: 'labor' | 'parts' | 'other';
  description: string;
  quantity: number;
  unit_price: number;
  total_price: number;
}

export interface MerchantInvoice {
  id: string;
  invoice_number: string;
  work_order_id: string;
  quote_id: string;
  amount: number;
  tax_amount: number;
  total_amount: number;
  status: 'draft' | 'submitted' | 'approved' | 'paid' | 'rejected';
  due_date: string;
  issued_date: string;
  paid_date?: string;
  line_items: InvoiceLineItem[];
  attachments: string[];
  created_at: string;
}

export interface InvoiceLineItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  total_price: number;
}

export const merchantApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get merchant work orders
    getMerchantWorkOrders: builder.query<PaginatedResponse<MerchantWorkOrder>, {
      page?: number;
      limit?: number;
      status?: string;
      priority?: string;
      date_from?: string;
      date_to?: string;
    }>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) params.append(key, String(value));
        });
        return {
          url: '/merchant/work-orders',
          params: Object.fromEntries(params),
        };
      },
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: 'Merchant' as const, id })),
              { type: 'Merchant', id: 'WORK_ORDERS' },
            ]
          : [{ type: 'Merchant', id: 'WORK_ORDERS' }],
    }),

    // Get single work order
    getMerchantWorkOrder: builder.query<MerchantWorkOrder, string>({
      query: (id) => `/merchant/work-orders/${id}`,
      providesTags: (result, error, id) => [{ type: 'Merchant', id }],
    }),

    // Update work order status
    updateMerchantWorkOrderStatus: builder.mutation<MerchantWorkOrder, {
      id: string;
      status: string;
      notes?: string;
      completion_date?: string;
      actual_cost?: number;
    }>({
      query: ({ id, ...data }) => ({
        url: `/merchant/work-orders/${id}/status`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Merchant', id },
        { type: 'Merchant', id: 'WORK_ORDERS' },
      ],
    }),

    // Submit quote
    submitQuote: builder.mutation<MerchantQuote, {
      work_order_id: string;
      labor_cost: number;
      parts_cost: number;
      other_costs: number;
      estimated_completion_date: string;
      line_items: Omit<QuoteLineItem, 'id'>[];
      notes?: string;
    }>({
      query: (data) => ({
        url: '/merchant/quotes',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { work_order_id }) => [
        { type: 'Merchant', id: work_order_id },
        { type: 'Merchant', id: 'WORK_ORDERS' },
      ],
    }),

    // Get merchant quotes
    getMerchantQuotes: builder.query<PaginatedResponse<MerchantQuote>, {
      page?: number;
      limit?: number;
      status?: string;
      work_order_id?: string;
    }>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) params.append(key, String(value));
        });
        return {
          url: '/merchant/quotes',
          params: Object.fromEntries(params),
        };
      },
      providesTags: [{ type: 'Merchant', id: 'QUOTES' }],
    }),

    // Update quote
    updateQuote: builder.mutation<MerchantQuote, {
      id: string;
      data: Partial<{
        labor_cost: number;
        parts_cost: number;
        other_costs: number;
        estimated_completion_date: string;
        line_items: Omit<QuoteLineItem, 'id'>[];
        notes: string;
      }>;
    }>({
      query: ({ id, data }) => ({
        url: `/merchant/quotes/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Merchant', id },
        { type: 'Merchant', id: 'QUOTES' },
      ],
    }),

    // Get merchant invoices
    getMerchantInvoices: builder.query<PaginatedResponse<MerchantInvoice>, {
      page?: number;
      limit?: number;
      status?: string;
      date_from?: string;
      date_to?: string;
    }>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) params.append(key, String(value));
        });
        return {
          url: '/merchant/invoices',
          params: Object.fromEntries(params),
        };
      },
      providesTags: [{ type: 'Merchant', id: 'INVOICES' }],
    }),

    // Create invoice from work order
    createInvoiceFromWorkOrder: builder.mutation<MerchantInvoice, {
      work_order_id: string;
      due_date: string;
      attachments?: File[];
    }>({
      query: ({ attachments, ...data }) => {
        if (attachments && attachments.length > 0) {
          const formData = new FormData();
          Object.entries(data).forEach(([key, value]) => {
            formData.append(key, String(value));
          });
          attachments.forEach((file, index) => {
            formData.append(`attachment_${index}`, file);
          });
          return {
            url: '/merchant/invoices',
            method: 'POST',
            body: formData,
          };
        }
        return {
          url: '/merchant/invoices',
          method: 'POST',
          body: data,
        };
      },
      invalidatesTags: [{ type: 'Merchant', id: 'INVOICES' }],
    }),

    // Submit invoice
    submitInvoice: builder.mutation<MerchantInvoice, string>({
      query: (id) => ({
        url: `/merchant/invoices/${id}/submit`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Merchant', id },
        { type: 'Merchant', id: 'INVOICES' },
      ],
    }),

    // Get payment status
    getPaymentStatus: builder.query<{
      total_outstanding: number;
      total_paid: number;
      overdue_amount: number;
      recent_payments: Array<{
        invoice_id: string;
        amount: number;
        paid_date: string;
        reference: string;
      }>;
    }, void>({
      query: () => '/merchant/payments/status',
      providesTags: [{ type: 'Merchant', id: 'PAYMENTS' }],
    }),

    // Get merchant dashboard stats
    getMerchantDashboardStats: builder.query<{
      active_work_orders: number;
      pending_quotes: number;
      pending_invoices: number;
      monthly_revenue: number;
      completion_rate: number;
      average_rating: number;
    }, void>({
      query: () => '/merchant/dashboard/stats',
      providesTags: [{ type: 'Merchant', id: 'STATS' }],
    }),
  }),
});

export const {
  useGetMerchantWorkOrdersQuery,
  useGetMerchantWorkOrderQuery,
  useUpdateMerchantWorkOrderStatusMutation,
  useSubmitQuoteMutation,
  useGetMerchantQuotesQuery,
  useUpdateQuoteMutation,
  useGetMerchantInvoicesQuery,
  useCreateInvoiceFromWorkOrderMutation,
  useSubmitInvoiceMutation,
  useGetPaymentStatusQuery,
  useGetMerchantDashboardStatsQuery,
} = merchantApi;