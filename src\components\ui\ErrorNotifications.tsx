import React from 'react';
import { Alert } from './alert';
import { X } from 'lucide-react';
import type { ApiError } from '@/store/slices/errorSlice';

interface Props {
  errors: ApiError[];
  globalErrorId?: string;
  onDismiss: (id: string) => void;
}

export const ErrorNotifications: React.FC<Props> = ({ errors, globalErrorId, onDismiss }) => {
  const visibleErrors = errors.filter(error => error.id !== globalErrorId);
  if (visibleErrors.length === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-40 space-y-2 max-w-sm">
      {visibleErrors.slice(-3).map(error => (
        <Alert
          key={error.id}
          variant={error.retryable ? "default" : "destructive"}
          className="relative pr-12"
        >
          <button
            onClick={() => onDismiss(error.id)}
            className="absolute right-4 top-4 opacity-70 ring-offset-background transition-opacity hover:opacity-100"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Dismiss</span>
          </button>
          <div className="text-sm font-medium">
            {error.message}
          </div>
        </Alert>
      ))}
    </div>
  );
};
