import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Upload, 
  FileText, 
  AlertTriangle, 
  Calculator,
  Clock,
  Shield,
  Trash2,
  Plus,
  Car,
  MapPin,
  Calendar
} from 'lucide-react';

interface QuoteItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
  type: 'labor' | 'parts';
}

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
}

const QuoteSubmissionPage: React.FC = () => {
  const [workOrderId] = useState('WO-2025-001'); // From URL params
  const [quoteItems, setQuoteItems] = useState<QuoteItem[]>([
    { id: '1', description: 'Brake pad replacement', quantity: 1, unitPrice: 800, total: 800, type: 'labor' },
    { id: '2', description: 'Brake pads (front set)', quantity: 1, unitPrice: 450, total: 450, type: 'parts' }
  ]);
  
  const [duration, setDuration] = useState('4');
  const [warrantyPeriod, setWarrantyPeriod] = useState('6');
  const [warrantyType, setWarrantyType] = useState('parts_labor');
  const [notes, setNotes] = useState('');
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [fraudDetectionAccepted, setFraudDetectionAccepted] = useState(false);

  // Mock work order data
  const workOrder = {
    id: 'WO-2025-001',
    vehicle: 'Toyota Hilux',
    registration: 'GP123ABC',
    type: 'Brake Service',
    priority: 'High',
    dueDate: '2025-01-15',
    location: 'Pretoria Central',
    description: 'Complete brake pad replacement and brake fluid change',
    department: 'Department of Health'
  };

  const addQuoteItem = (type: 'labor' | 'parts') => {
    const newItem: QuoteItem = {
      id: Date.now().toString(),
      description: '',
      quantity: 1,
      unitPrice: 0,
      total: 0,
      type
    };
    setQuoteItems([...quoteItems, newItem]);
  };

  const updateQuoteItem = (id: string, field: keyof QuoteItem, value: any) => {
    setQuoteItems(items => items.map(item => {
      if (item.id === id) {
        const updated = { ...item, [field]: value };
        if (field === 'quantity' || field === 'unitPrice') {
          updated.total = updated.quantity * updated.unitPrice;
        }
        return updated;
      }
      return item;
    }));
  };

  const removeQuoteItem = (id: string) => {
    setQuoteItems(items => items.filter(item => item.id !== id));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      Array.from(files).forEach(file => {
        const newFile: UploadedFile = {
          id: Date.now().toString() + Math.random(),
          name: file.name,
          size: file.size,
          type: file.type
        };
        setUploadedFiles(prev => [...prev, newFile]);
      });
    }
  };

  const removeFile = (id: string) => {
    setUploadedFiles(files => files.filter(file => file.id !== id));
  };

  const calculateSubtotals = () => {
    const laborTotal = quoteItems.filter(item => item.type === 'labor').reduce((sum, item) => sum + item.total, 0);
    const partsTotal = quoteItems.filter(item => item.type === 'parts').reduce((sum, item) => sum + item.total, 0);
    const subtotal = laborTotal + partsTotal;
    const vat = subtotal * 0.15; // 15% VAT
    const total = subtotal + vat;
    
    return { laborTotal, partsTotal, subtotal, vat, total };
  };

  const { laborTotal, partsTotal, subtotal, vat, total } = calculateSubtotals();

  const handleSubmitQuote = () => {
    console.log('Submitting quote:', {
      workOrderId,
      items: quoteItems,
      duration,
      warranty: { period: warrantyPeriod, type: warrantyType },
      notes,
      files: uploadedFiles,
      totals: { laborTotal, partsTotal, subtotal, vat, total }
    });
  };

  return (
    <div className="container mx-auto px-6 py-8 space-y-6 max-w-7xl">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Submit Quote</h1>
          <p className="text-gray-600">Provide detailed quote for work order {workOrder.id}</p>
        </div>
        <Badge variant="outline" className="text-sm">
          Due: {new Date(workOrder.dueDate).toLocaleDateString()}
        </Badge>
      </div>

      {/* Work Order Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Work Order Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center text-sm">
              <Car className="h-4 w-4 mr-2 text-gray-500" />
              {workOrder.vehicle} - {workOrder.registration}
            </div>
            <div className="flex items-center text-sm">
              <MapPin className="h-4 w-4 mr-2 text-gray-500" />
              {workOrder.location}
            </div>
            <div className="flex items-center text-sm">
              <Calendar className="h-4 w-4 mr-2 text-gray-500" />
              {workOrder.department}
            </div>
          </div>
          <div className="mt-4">
            <p className="text-sm"><strong>Service Type:</strong> {workOrder.type}</p>
            <p className="text-sm mt-1"><strong>Description:</strong> {workOrder.description}</p>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quote Items */}
        <div className="lg:col-span-2 space-y-6">
          {/* Labor Items */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="flex items-center">
                  <Calculator className="h-5 w-5 mr-2" />
                  Labor Costs
                </CardTitle>
                <Button size="sm" variant="outline" onClick={() => addQuoteItem('labor')}>
                  <Plus className="h-4 w-4 mr-1" />
                  Add Labor
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {quoteItems.filter(item => item.type === 'labor').map((item) => (
                  <div key={item.id} className="grid grid-cols-12 gap-2 items-center">
                    <div className="col-span-5">
                      <Input
                        placeholder="Labor description"
                        value={item.description}
                        onChange={(e) => updateQuoteItem(item.id, 'description', e.target.value)}
                      />
                    </div>
                    <div className="col-span-2">
                      <Input
                        type="number"
                        placeholder="Hours"
                        value={item.quantity}
                        onChange={(e) => updateQuoteItem(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                      />
                    </div>
                    <div className="col-span-2">
                      <Input
                        type="number"
                        placeholder="Rate/hour"
                        value={item.unitPrice}
                        onChange={(e) => updateQuoteItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                      />
                    </div>
                    <div className="col-span-2">
                      <Input
                        value={`R${item.total.toFixed(2)}`}
                        disabled
                        className="bg-gray-50"
                      />
                    </div>
                    <div className="col-span-1">
                      <Button size="sm" variant="ghost" onClick={() => removeQuoteItem(item.id)}>
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Parts Items */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Parts & Materials</CardTitle>
                <Button size="sm" variant="outline" onClick={() => addQuoteItem('parts')}>
                  <Plus className="h-4 w-4 mr-1" />
                  Add Part
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {quoteItems.filter(item => item.type === 'parts').map((item) => (
                  <div key={item.id} className="grid grid-cols-12 gap-2 items-center">
                    <div className="col-span-5">
                      <Input
                        placeholder="Part description"
                        value={item.description}
                        onChange={(e) => updateQuoteItem(item.id, 'description', e.target.value)}
                      />
                    </div>
                    <div className="col-span-2">
                      <Input
                        type="number"
                        placeholder="Qty"
                        value={item.quantity}
                        onChange={(e) => updateQuoteItem(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                      />
                    </div>
                    <div className="col-span-2">
                      <Input
                        type="number"
                        placeholder="Unit price"
                        value={item.unitPrice}
                        onChange={(e) => updateQuoteItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                      />
                    </div>
                    <div className="col-span-2">
                      <Input
                        value={`R${item.total.toFixed(2)}`}
                        disabled
                        className="bg-gray-50"
                      />
                    </div>
                    <div className="col-span-1">
                      <Button size="sm" variant="ghost" onClick={() => removeQuoteItem(item.id)}>
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Duration & Warranty */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                Duration & Warranty
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="duration">Estimated Duration (hours)</Label>
                  <Input
                    id="duration"
                    type="number"
                    value={duration}
                    onChange={(e) => setDuration(e.target.value)}
                    placeholder="e.g., 4"
                  />
                </div>
                <div>
                  <Label htmlFor="warranty-period">Warranty Period (months)</Label>
                  <Input
                    id="warranty-period"
                    type="number"
                    value={warrantyPeriod}
                    onChange={(e) => setWarrantyPeriod(e.target.value)}
                    placeholder="e.g., 6"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="warranty-type">Warranty Coverage</Label>
                <Select value={warrantyType} onValueChange={setWarrantyType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="parts_only">Parts Only</SelectItem>
                    <SelectItem value="labor_only">Labor Only</SelectItem>
                    <SelectItem value="parts_labor">Parts & Labor</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* File Uploads */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Upload className="h-5 w-5 mr-2" />
                Supporting Documents
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <input
                    type="file"
                    multiple
                    onChange={handleFileUpload}
                    className="hidden"
                    id="file-upload"
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  />
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-600">
                      Click to upload quotes, supplier docs, or images
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      PDF, DOC, JPG, PNG up to 10MB each
                    </p>
                  </label>
                </div>
                
                {uploadedFiles.length > 0 && (
                  <div className="space-y-2">
                    {uploadedFiles.map((file) => (
                      <div key={file.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 mr-2 text-gray-500" />
                          <span className="text-sm">{file.name}</span>
                          <span className="text-xs text-gray-500 ml-2">
                            ({(file.size / 1024).toFixed(1)} KB)
                          </span>
                        </div>
                        <Button size="sm" variant="ghost" onClick={() => removeFile(file.id)}>
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Additional Notes */}
          <Card>
            <CardHeader>
              <CardTitle>Additional Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="Any additional information, special requirements, or terms..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={4}
              />
            </CardContent>
          </Card>
        </div>

        {/* Quote Summary */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Quote Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Labor Total:</span>
                  <span>R{laborTotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Parts Total:</span>
                  <span>R{partsTotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm border-t pt-2">
                  <span>Subtotal:</span>
                  <span>R{subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>VAT (15%):</span>
                  <span>R{vat.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-lg font-bold border-t pt-2">
                  <span>Total:</span>
                  <span>R{total.toFixed(2)}</span>
                </div>
              </div>
              
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-2" />
                  Duration: {duration} hours
                </div>
                <div className="flex items-center">
                  <Shield className="h-4 w-4 mr-2" />
                  Warranty: {warrantyPeriod} months ({warrantyType.replace('_', ' & ')})
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Fraud Detection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-orange-600">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Fraud Detection
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded">
                  <p className="text-sm text-yellow-800">
                    <strong>Price Analysis:</strong> Your quote is 15% above market average for similar services.
                  </p>
                </div>
                <div className="p-3 bg-green-50 border border-green-200 rounded">
                  <p className="text-sm text-green-800">
                    <strong>Vendor Verification:</strong> All credentials verified ✓
                  </p>
                </div>
                
                <div className="flex items-start space-x-2">
                  <Checkbox
                    id="fraud-detection"
                    checked={fraudDetectionAccepted}
                    onCheckedChange={(checked) => setFraudDetectionAccepted(checked === true)}
                  />
                  <label htmlFor="fraud-detection" className="text-sm">
                    I acknowledge the fraud detection analysis and confirm this quote is accurate and competitive.
                  </label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <Button 
            className="w-full" 
            size="lg"
            onClick={handleSubmitQuote}
            disabled={!fraudDetectionAccepted || quoteItems.length === 0}
          >
            Submit Quote
          </Button>
        </div>
      </div>
    </div>
  );
};

export default QuoteSubmissionPage;

