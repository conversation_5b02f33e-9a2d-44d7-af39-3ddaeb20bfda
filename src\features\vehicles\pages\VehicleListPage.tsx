
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { VehicleList } from '../components/VehicleList';
import { mockVehicles } from '@/utils/mockData';
import type { VehicleFilters } from '@/types/api';

export const VehicleListPage: React.FC = () => {
  const navigate = useNavigate();
  const [filters, setFilters] = useState<VehicleFilters>({
    page: 1,
    limit: 20,
  });

  // Use mock data instead of API
  const vehicles = mockVehicles;
  const isLoading = false;

  const handleFiltersChange = (newFilters: VehicleFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  return (
    <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6">
      <VehicleList
        vehicles={vehicles}
        loading={isLoading}
        onView={(id) => navigate(`/vehicles/${id}`)}
        onEdit={(id) => navigate(`/vehicles/${id}/edit`)}
        onAdd={() => navigate('/vehicles/add')}
        onScheduleService={(id) => navigate(`/vehicles/${id}/schedule-service`)}
        onFiltersChange={handleFiltersChange}
        onPageChange={handlePageChange}
        onRefresh={() => {}}
      />
    </div>
  );
};


