import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Calendar, MapPin, Clock, Fuel, Filter, Search } from 'lucide-react';
import { useNotifications } from '@/hooks/useNotifications';

interface Trip {
  id: string;
  date: string;
  startTime: string;
  endTime: string;
  startLocation: string;
  endLocation: string;
  distance: number;
  duration: string;
  purpose: string;
  vehicle: string;
  fuelUsed: number;
  status: 'completed' | 'in_progress' | 'cancelled';
}

const TripHistoryPage: React.FC = () => {
  const navigate = useNavigate();
  const { showInfo, showSuccess } = useNotifications();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterPeriod, setFilterPeriod] = useState('all');
  const [showFilters, setShowFilters] = useState(false);

  // Mock data - replace with API call
  const [trips] = useState<Trip[]>([
    {
      id: 'T001',
      date: '2025-01-14',
      startTime: '08:30',
      endTime: '11:45',
      startLocation: 'Main Depot',
      endLocation: 'Johannesburg Hospital',
      distance: 45.2,
      duration: '3h 15m',
      purpose: 'Medical Supply Delivery',
      vehicle: 'GP 123 ABC',
      fuelUsed: 8.5,
      status: 'completed'
    },
    {
      id: 'T002',
      date: '2025-01-14',
      startTime: '14:00',
      endTime: '15:30',
      startLocation: 'Johannesburg Hospital',
      endLocation: 'Pretoria Office',
      distance: 28.7,
      duration: '1h 30m',
      purpose: 'Document Collection',
      vehicle: 'GP 123 ABC',
      fuelUsed: 5.2,
      status: 'completed'
    },
    {
      id: 'T003',
      date: '2025-01-13',
      startTime: '09:00',
      endTime: '12:30',
      startLocation: 'Main Depot',
      endLocation: 'School District A',
      distance: 62.1,
      duration: '3h 30m',
      purpose: 'Equipment Transport',
      vehicle: 'GP 123 ABC',
      fuelUsed: 12.8,
      status: 'completed'
    },
    {
      id: 'T004',
      date: '2025-01-12',
      startTime: '10:15',
      endTime: '',
      startLocation: 'Main Depot',
      endLocation: 'Municipal Office',
      distance: 0,
      duration: '',
      purpose: 'Official Meeting',
      vehicle: 'GP 123 ABC',
      fuelUsed: 0,
      status: 'cancelled'
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'in_progress': return 'text-blue-600 bg-blue-100';
      case 'cancelled': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const filteredTrips = trips.filter(trip => {
    const matchesSearch = trip.endLocation.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         trip.purpose.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         trip.vehicle.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (filterPeriod === 'all') return matchesSearch;
    
    const tripDate = new Date(trip.date);
    const now = new Date();
    
    switch (filterPeriod) {
      case 'today':
        return matchesSearch && tripDate.toDateString() === now.toDateString();
      case 'week':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        return matchesSearch && tripDate >= weekAgo;
      case 'month':
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        return matchesSearch && tripDate >= monthAgo;
      default:
        return matchesSearch;
    }
  });

  const totalDistance = filteredTrips.reduce((sum, trip) => sum + trip.distance, 0);
  const totalFuel = filteredTrips.reduce((sum, trip) => sum + trip.fuelUsed, 0);
  const completedTrips = filteredTrips.filter(trip => trip.status === 'completed').length;

  // Show summary notification on load
  useEffect(() => {
    if (filteredTrips.length > 0) {
      const completedCount = filteredTrips.filter(trip => trip.status === 'completed').length;
      showInfo('Trip Summary', `Showing ${filteredTrips.length} trips (${completedCount} completed)`);
    }
  }, [filteredTrips.length]);

  // Handle trip actions
  const handleTripAction = (trip: Trip, action: string) => {
    switch (action) {
      case 'view-details':
        showInfo('Trip Details', `${trip.purpose} - ${trip.distance}km in ${trip.duration}`);
        break;
      case 'report-issue':
        navigate('/driver/report-issue', { state: { tripId: trip.id } });
        break;
      case 'add-fuel-log':
        navigate('/driver/fuel-log', { state: { tripId: trip.id } });
        break;
    }
  };

  // Add action buttons to trip cards
  const renderTripActions = (trip: Trip) => {
    if (trip.status === 'completed') {
      return (
        <div className="mt-3 pt-2 border-t border-gray-100 flex space-x-2">
          <button
            onClick={() => handleTripAction(trip, 'view-details')}
            className="text-xs text-blue-600 hover:text-blue-800"
          >
            View Details
          </button>
          <button
            onClick={() => handleTripAction(trip, 'report-issue')}
            className="text-xs text-orange-600 hover:text-orange-800"
          >
            Report Issue
          </button>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button onClick={() => navigate(-1)} className="p-1">
              <ArrowLeft className="h-6 w-6" />
            </button>
            <h1 className="text-xl font-bold">Trip History</h1>
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="p-2 rounded-full bg-blue-700 hover:bg-blue-800"
          >
            <Filter className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Search & Filters */}
      <div className="bg-white p-4 shadow-sm">
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search trips..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {showFilters && (
          <div className="grid grid-cols-4 gap-2">
            {[
              { id: 'all', name: 'All' },
              { id: 'today', name: 'Today' },
              { id: 'week', name: 'Week' },
              { id: 'month', name: 'Month' }
            ].map((period) => (
              <button
                key={period.id}
                onClick={() => setFilterPeriod(period.id)}
                className={`py-2 px-3 rounded-lg text-sm font-medium ${
                  filterPeriod === period.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {period.name}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Summary Stats */}
      <div className="p-4 grid grid-cols-3 gap-4">
        <div className="bg-white rounded-lg p-3 shadow-sm text-center">
          <p className="text-2xl font-bold text-blue-600">{completedTrips}</p>
          <p className="text-xs text-gray-600">Completed</p>
        </div>
        <div className="bg-white rounded-lg p-3 shadow-sm text-center">
          <p className="text-2xl font-bold text-green-600">{totalDistance.toFixed(1)}</p>
          <p className="text-xs text-gray-600">km Driven</p>
        </div>
        <div className="bg-white rounded-lg p-3 shadow-sm text-center">
          <p className="text-2xl font-bold text-orange-600">{totalFuel.toFixed(1)}</p>
          <p className="text-xs text-gray-600">L Fuel</p>
        </div>
      </div>

      {/* Trip List */}
      <div className="px-4 pb-6">
        <div className="space-y-3">
          {filteredTrips.map((trip) => (
            <div key={trip.id} className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium text-gray-900">
                      {new Date(trip.date).toLocaleDateString('en-ZA')}
                    </span>
                    <span className="text-sm text-gray-500">
                      {trip.startTime} - {trip.endTime || 'Ongoing'}
                    </span>
                  </div>
                  <h3 className="font-medium text-gray-900">{trip.purpose}</h3>
                  <p className="text-sm text-gray-600">{trip.vehicle}</p>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(trip.status)}`}>
                  {trip.status.replace('_', ' ').toUpperCase()}
                </span>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <MapPin className="h-4 w-4" />
                  <span>{trip.startLocation} → {trip.endLocation}</span>
                </div>

                {trip.status === 'completed' && (
                  <div className="grid grid-cols-3 gap-4 pt-2 border-t border-gray-100">
                    <div className="flex items-center space-x-1">
                      <MapPin className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-600">{trip.distance} km</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-600">{trip.duration}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Fuel className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-600">{trip.fuelUsed}L</span>
                    </div>
                  </div>
                )}
              </div>
              {renderTripActions(trip)}
            </div>
          ))}
        </div>

        {filteredTrips.length === 0 && (
          <div className="text-center py-8">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No trips found</h3>
            <p className="text-gray-600">
              {searchTerm ? 'Try adjusting your search terms' : 'No trips match the selected filters'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TripHistoryPage;
