export type UserRole = 
  | 'fleet_manager' 
  | 'transport_officer' 
  | 'finance_officer' 
  | 'executive' 
  | 'auditor' 
  | 'driver' 
  | 'vendor'
  | 'admin'
  | 'merchant'
  | 'inspector';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  department?: string;
  permissions: string[];
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Vehicle {
  id: string;
  registration: string;
  make: string;
  model: string;
  year: number;
  vin: string;
  status: 'active' | 'maintenance' | 'out_of_service' | 'retired';
  department: string;
  assignedDriver?: string;
  mileage: number;
  fuelType: string;
  engineSize: string;
  color: string;
  purchaseDate: string;
  purchasePrice: number;
  currentValue: number;
  insuranceExpiry: string;
  licenseExpiry: string;
  lastService: string;
  nextService: string;
  location: string;
}

export interface WorkOrder {
  id: string;
  workOrderNumber: string;
  title: string;
  description: string;
  type: 'maintenance' | 'repair' | 'inspection' | 'accident';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'approved' | 'assigned' | 'in_progress' | 'completed' | 'rejected';
  requestDate: string;
  requestedBy: string;
  department: string;
  vehicleId: string;
  assignedVendorId?: string;
  estimatedCost?: number;
  actualCost?: number;
  scheduledDate?: string;
  completedDate?: string;
  approvedBy?: string;
  approvalDate?: string;
}

export interface Vendor {
  id: string;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  province: string;
  specializations: string[];
  rating: number;
  totalJobs: number;
  completedJobs: number;
  averageCost: number;
  averageCompletionTime: number;
  status: 'active' | 'inactive' | 'suspended' | 'pending_approval';
  registrationDate: string;
  lastJobDate?: string;
  bbbeeLevel: number;
  certifications: string[];
  complianceScore: number;
}


