import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Search, 
  Eye, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Building, 
  User, 
  Mail, 
  Phone,
  MapPin,
  FileText
} from 'lucide-react';

interface DepartmentApplication {
  id: string;
  applicationId: string;
  // Entity Information
  entityName: string;
  entityType: string;
  registrationNumber: string;
  
  // Address Information
  physicalAddress: string;
  postalAddress: string;
  city: string;
  province: string;
  postalCode: string;
  
  // Primary Official
  primaryOfficialName: string;
  primaryOfficialTitle: string;
  primaryOfficialEmail: string;
  primaryOfficialPhone: string;
  
  // Secondary Official
  secondaryOfficialName?: string;
  secondaryOfficialTitle?: string;
  secondaryOfficialEmail?: string;
  secondaryOfficialPhone?: string;
  
  // Billing Information
  billingCutOffDate: string;
  billingFrequency: string;
  billingContactName: string;
  billingContactEmail: string;
  billingContactPhone: string;
  
  // Department Mandate
  departmentMandate: string;
  budgetAllocation: number;
  fleetSize: number;
  
  // Application Status
  status: 'pending' | 'approved' | 'rejected';
  submittedDate: string;
  reviewedBy?: string;
  reviewedDate?: string;
  reviewNotes?: string;
}

const PendingDepartmentApplications: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedApplication, setSelectedApplication] = useState<DepartmentApplication | null>(null);

  // Mock data - replace with actual API call
  const [applications, setApplications] = useState<DepartmentApplication[]>([
    {
      id: '1',
      applicationId: 'APP-1703123456',
      entityName: 'Department of Transport - Western Cape',
      entityType: 'provincial',
      primaryOfficialName: 'John Smith',
      primaryOfficialEmail: '<EMAIL>',
      primaryOfficialPhone: '+27 21 123 4567',
      city: 'Cape Town',
      province: 'western-cape',
      fleetSize: 150,
      budgetAllocation: 25000000,
      status: 'pending',
      submittedDate: '2024-01-15T10:30:00Z',
      registrationNumber: '',
      physicalAddress: '',
      postalAddress: '',
      postalCode: '',
      primaryOfficialTitle: '',
      billingCutOffDate: '',
      billingFrequency: '',
      billingContactName: '',
      billingContactEmail: '',
      billingContactPhone: '',
      departmentMandate: ''
    },
    {
      id: '2',
      applicationId: 'APP-1703123457',
      entityName: 'Johannesburg Metropolitan Municipality',
      entityType: 'municipal',
      primaryOfficialName: 'Sarah Johnson',
      primaryOfficialEmail: '<EMAIL>',
      primaryOfficialPhone: '+27 11 987 6543',
      city: 'Johannesburg',
      province: 'gauteng',
      fleetSize: 300,
      budgetAllocation: 45000000,
      status: 'pending',
      submittedDate: '2024-01-14T14:20:00Z',
      registrationNumber: '',
      physicalAddress: '',
      postalAddress: '',
      postalCode: '',
      primaryOfficialTitle: '',
      billingCutOffDate: '',
      billingFrequency: '',
      billingContactName: '',
      billingContactEmail: '',
      billingContactPhone: '',
      departmentMandate: ''
    }
  ]);

  const handleApprove = (applicationId: string) => {
    setApplications(prev => prev.map(app => 
      app.id === applicationId 
        ? { ...app, status: 'approved', reviewedDate: new Date().toISOString(), reviewedBy: 'Current Admin' }
        : app
    ));
    setSelectedApplication(null);
  };

  const handleReject = (applicationId: string) => {
    setApplications(prev => prev.map(app => 
      app.id === applicationId 
        ? { ...app, status: 'rejected', reviewedDate: new Date().toISOString(), reviewedBy: 'Current Admin' }
        : app
    ));
    setSelectedApplication(null);
  };

  const filteredApplications = applications.filter(app => {
    const matchesSearch = app.entityName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.applicationId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || app.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600"><Clock className="h-3 w-3 mr-1" />Pending</Badge>;
      case 'approved':
        return <Badge variant="outline" className="text-green-600 border-green-600"><CheckCircle className="h-3 w-3 mr-1" />Approved</Badge>;
      case 'rejected':
        return <Badge variant="outline" className="text-red-600 border-red-600"><XCircle className="h-3 w-3 mr-1" />Rejected</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Department Applications</h1>
        <p className="text-gray-600">Review and approve department onboarding applications</p>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by entity name or application ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Applications List */}
      <div className="grid gap-4">
        {filteredApplications.map((application) => (
          <Card key={application.id} className="hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{application.entityName}</h3>
                  <p className="text-sm text-gray-500">Application ID: {application.applicationId}</p>
                </div>
                {getStatusBadge(application.status)}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4 text-gray-400" />
                  <span className="text-sm capitalize">{application.entityType.replace('-', ' ')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{application.primaryOfficialName}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{application.city}, {application.province}</span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{application.primaryOfficialEmail}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{application.primaryOfficialPhone}</span>
                </div>
                <div className="text-sm">
                  <span className="text-gray-500">Fleet Size:</span> {application.fleetSize} vehicles
                </div>
              </div>

              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-500">
                  Submitted: {new Date(application.submittedDate).toLocaleDateString()}
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedApplication(application)}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View Details
                  </Button>
                  {application.status === 'pending' && (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleApprove(application.id)}
                        className="text-green-600 border-green-600 hover:bg-green-50"
                      >
                        <CheckCircle className="h-4 w-4 mr-1" />
                        Approve
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleReject(application.id)}
                        className="text-red-600 border-red-600 hover:bg-red-50"
                      >
                        <XCircle className="h-4 w-4 mr-1" />
                        Reject
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredApplications.length === 0 && (
        <Card>
          <CardContent className="pt-6 text-center">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No applications found</h3>
            <p className="text-gray-500">No department applications match your current filters.</p>
          </CardContent>
        </Card>
      )}

      {/* Application Detail Modal/Sidebar would go here */}
      {selectedApplication && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <CardHeader className="border-b">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-xl">{selectedApplication.entityName}</CardTitle>
                  <p className="text-sm text-gray-500">Application ID: {selectedApplication.applicationId}</p>
                </div>
                {getStatusBadge(selectedApplication.status)}
              </div>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                
                {/* Entity Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Building className="h-5 w-5" />
                      Entity Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Entity Name</label>
                      <p className="text-gray-900">{selectedApplication.entityName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Entity Type</label>
                      <p className="text-gray-900 capitalize">{selectedApplication.entityType.replace('-', ' ')}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Registration Number</label>
                      <p className="text-gray-900">{selectedApplication.registrationNumber || 'Not provided'}</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Address Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <MapPin className="h-5 w-5" />
                      Address Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Physical Address</label>
                      <p className="text-gray-900">{selectedApplication.physicalAddress || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Postal Address</label>
                      <p className="text-gray-900">{selectedApplication.postalAddress || 'Not provided'}</p>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">City</label>
                        <p className="text-gray-900">{selectedApplication.city}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Province</label>
                        <p className="text-gray-900 capitalize">{selectedApplication.province.replace('-', ' ')}</p>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Postal Code</label>
                      <p className="text-gray-900">{selectedApplication.postalCode || 'Not provided'}</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Primary Official */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <User className="h-5 w-5" />
                      Primary Official
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Full Name</label>
                      <p className="text-gray-900">{selectedApplication.primaryOfficialName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Title/Position</label>
                      <p className="text-gray-900">{selectedApplication.primaryOfficialTitle || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Email</label>
                      <p className="text-gray-900">{selectedApplication.primaryOfficialEmail}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Phone</label>
                      <p className="text-gray-900">{selectedApplication.primaryOfficialPhone}</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Secondary Official */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <User className="h-5 w-5" />
                      Secondary Official
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {selectedApplication.secondaryOfficialName ? (
                      <>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Full Name</label>
                          <p className="text-gray-900">{selectedApplication.secondaryOfficialName}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Title/Position</label>
                          <p className="text-gray-900">{selectedApplication.secondaryOfficialTitle || 'Not provided'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Email</label>
                          <p className="text-gray-900">{selectedApplication.secondaryOfficialEmail || 'Not provided'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Phone</label>
                          <p className="text-gray-900">{selectedApplication.secondaryOfficialPhone || 'Not provided'}</p>
                        </div>
                      </>
                    ) : (
                      <p className="text-gray-500 italic">No secondary official provided</p>
                    )}
                  </CardContent>
                </Card>

                {/* Billing Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Billing Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Billing Cut-off Date</label>
                      <p className="text-gray-900">{selectedApplication.billingCutOffDate || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Billing Frequency</label>
                      <p className="text-gray-900 capitalize">{selectedApplication.billingFrequency || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Billing Contact Name</label>
                      <p className="text-gray-900">{selectedApplication.billingContactName || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Billing Contact Email</label>
                      <p className="text-gray-900">{selectedApplication.billingContactEmail || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Billing Contact Phone</label>
                      <p className="text-gray-900">{selectedApplication.billingContactPhone || 'Not provided'}</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Fleet & Budget Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Building className="h-5 w-5" />
                      Fleet & Budget
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Expected Fleet Size</label>
                      <p className="text-gray-900">{selectedApplication.fleetSize} vehicles</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Annual Budget Allocation</label>
                      <p className="text-gray-900">R{selectedApplication.budgetAllocation.toLocaleString()}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Submitted Date</label>
                      <p className="text-gray-900">{new Date(selectedApplication.submittedDate).toLocaleDateString()}</p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Department Mandate - Full Width */}
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Department Mandate & Objectives
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-gray-900 whitespace-pre-wrap">
                      {selectedApplication.departmentMandate || 'No mandate provided'}
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Review Section */}
              {selectedApplication.reviewedBy && (
                <Card className="mt-6">
                  <CardHeader>
                    <CardTitle className="text-lg">Review Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Reviewed By</label>
                      <p className="text-gray-900">{selectedApplication.reviewedBy}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Review Date</label>
                      <p className="text-gray-900">{selectedApplication.reviewedDate ? new Date(selectedApplication.reviewedDate).toLocaleDateString() : 'Not reviewed'}</p>
                    </div>
                    {selectedApplication.reviewNotes && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Review Notes</label>
                        <p className="text-gray-900">{selectedApplication.reviewNotes}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end gap-3 mt-6 pt-6 border-t">
                <Button variant="outline" onClick={() => setSelectedApplication(null)}>
                  Close
                </Button>
                {selectedApplication.status === 'pending' && (
                  <>
                    <Button 
                      onClick={() => handleApprove(selectedApplication.id)}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Approve Application
                    </Button>
                    <Button 
                      variant="destructive" 
                      onClick={() => handleReject(selectedApplication.id)}
                    >
                      <XCircle className="h-4 w-4 mr-2" />
                      Reject Application
                    </Button>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default PendingDepartmentApplications;

