import type { User } from '@/types/auth';

export interface MockCredentials {
  email: string;
  password: string;
  user: User;
}

export const mockUsers: MockCredentials[] = [
  // Fleet Manager
  {
    email: '<EMAIL>',
    password: 'FleetManager123!',
    user: {
      id: '1',
      email: '<EMAIL>',
      firstName: '<PERSON>',
      lastName: 'Fleet',
      role: 'fleet_manager',
      permissions: ['view_vehicles', 'manage_vehicles', 'view_work_orders', 'manage_work_orders', 'view_vendors', 'manage_vendors', 'view_reports', 'manage_reports', 'view_finances'],
      department: 'Fleet Management',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
    },
  },
  // Transport Officer
  {
    email: '<EMAIL>',
    password: 'Transport123!',
    user: {
      id: '2',
      email: '<EMAIL>',
      firstName: 'Sarah',
      lastName: 'Transport',
      role: 'transport_officer',
      permissions: ['view_vehicles', 'manage_vehicles', 'view_work_orders', 'manage_work_orders'],
      department: 'Transport Operations',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
    },
  },
  // Finance Officer
  {
    email: '<EMAIL>',
    password: 'Finance123!',
    user: {
      id: '3',
      email: '<EMAIL>',
      firstName: 'Michael',
      lastName: 'Finance',
      role: 'finance_officer',
      permissions: ['view_finances', 'manage_finances', 'view_reports'],
      department: 'Finance',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
    },
  },
  // Driver
  {
    email: '<EMAIL>',
    password: 'Driver123!',
    user: {
      id: '4',
      email: '<EMAIL>',
      firstName: 'David',
      lastName: 'Driver',
      role: 'driver',
      permissions: ['view_vehicles'],
      department: 'Operations',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
    },
  },
  // Auditor
  {
    email: '<EMAIL>',
    password: 'Auditor123!',
    user: {
      id: '5',
      email: '<EMAIL>',
      firstName: 'Lisa',
      lastName: 'Auditor',
      role: 'auditor',
      permissions: ['view_vehicles', 'view_work_orders', 'view_vendors', 'view_reports', 'view_finances'],
      department: 'Audit & Compliance',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
    },
  },
  // Admin
  {
    email: '<EMAIL>',
    password: 'Admin123!',
    user: {
      id: '6',
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'User',
      role: 'admin',
      permissions: ['view_vehicles', 'manage_vehicles', 'view_work_orders', 'manage_work_orders', 'view_vendors', 'manage_vendors', 'view_reports', 'manage_reports', 'view_finances', 'manage_finances', 'admin_access'],
      department: 'IT Administration',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
    },
  },
  // Merchant/Vendor
  {
    email: '<EMAIL>',
    password: 'Merchant123!',
    user: {
      id: '7',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Smith',
      role: 'merchant',
      permissions: ['view_work_orders', 'manage_quotes', 'submit_invoices', 'view_payments'],
      department: 'AutoCare Services',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
    },
  },
  // Inspector
  {
    email: '<EMAIL>',
    password: 'Inspector123!',
    user: {
      id: '8',
      email: '<EMAIL>',
      firstName: 'Maria',
      lastName: 'Inspector',
      role: 'inspector',
      permissions: ['view_vehicles', 'conduct_inspections', 'view_merchants', 'manage_inspections'],
      department: 'Quality Assurance',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
    },
  },
];

export const authenticateUser = (email: string, password: string): MockCredentials | null => {
  return mockUsers.find(user => user.email === email && user.password === password) || null;
};


