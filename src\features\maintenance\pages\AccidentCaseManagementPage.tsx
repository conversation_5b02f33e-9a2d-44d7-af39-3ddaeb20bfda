import React, { useState } from 'react';
import { Search, Plus, Eye, Edit, Clock, AlertCircle, CheckCircle, XCircle, Camera, FileText, User, Calendar } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface AccidentCase {
  id: string;
  vehicleId: string;
  vehicleReg: string;
  vehicleMake: string;
  vehicleModel: string;
  accidentDate: string;
  driverName: string;
  driverContact: string;
  location: string;
  description: string;
  severity: 'Minor' | 'Moderate' | 'Major' | 'Total Loss';
  status: 'Reported' | 'Assessed' | 'Quoted' | 'Approved' | 'In Repair' | 'Completed' | 'Closed';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  estimatedCost?: number;
  assessorAssigned?: string;
  photosCount: number;
  insuranceClaim?: string;
  reportedBy: string;
  reportDate: string;
}

const AccidentCaseManagementPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('All');
  const [severityFilter, setSeverityFilter] = useState<string>('All');
  const [priorityFilter, setPriorityFilter] = useState<string>('All');
  const navigate = useNavigate();

  // Mock data - replace with API call
  const mockCases: AccidentCase[] = [
    {
      id: 'AC-001',
      vehicleId: 'V-001',
      vehicleReg: 'GP 123 ABC',
      vehicleMake: 'Toyota',
      vehicleModel: 'Hilux',
      accidentDate: '2025-01-14',
      driverName: 'John Mthembu',
      driverContact: '082 123 4567',
      location: 'N1 Highway, Pretoria',
      description: 'Rear-end collision, damage to rear bumper and tailgate',
      severity: 'Moderate',
      status: 'Assessed',
      priority: 'High',
      estimatedCost: 25000,
      assessorAssigned: 'Sarah Johnson',
      photosCount: 8,
      insuranceClaim: 'INS-2025-001',
      reportedBy: 'John Mthembu',
      reportDate: '2025-01-14'
    },
    {
      id: 'AC-002',
      vehicleId: 'V-003',
      vehicleReg: 'GP 789 GHI',
      vehicleMake: 'Ford',
      vehicleModel: 'Ranger',
      accidentDate: '2025-01-13',
      driverName: 'Mary Ndaba',
      driverContact: '083 987 6543',
      location: 'Main Road, Johannesburg',
      description: 'Side impact collision, driver door and front fender damaged',
      severity: 'Major',
      status: 'Quoted',
      priority: 'Critical',
      estimatedCost: 45000,
      assessorAssigned: 'Mike Wilson',
      photosCount: 12,
      insuranceClaim: 'INS-2025-002',
      reportedBy: 'Transport Officer',
      reportDate: '2025-01-13'
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Reported': return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'Assessed': return <FileText className="w-4 h-4 text-blue-500" />;
      case 'Quoted': return <AlertCircle className="w-4 h-4 text-orange-500" />;
      case 'Approved': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'In Repair': return <AlertCircle className="w-4 h-4 text-purple-500" />;
      case 'Completed': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'Closed': return <XCircle className="w-4 h-4 text-gray-500" />;
      default: return null;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'Minor': return 'bg-green-100 text-green-800';
      case 'Moderate': return 'bg-yellow-100 text-yellow-800';
      case 'Major': return 'bg-orange-100 text-orange-800';
      case 'Total Loss': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredCases = mockCases.filter(accidentCase => {
    const matchesSearch = accidentCase.vehicleReg.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         accidentCase.driverName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         accidentCase.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         accidentCase.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'All' || accidentCase.status === statusFilter;
    const matchesSeverity = severityFilter === 'All' || accidentCase.severity === severityFilter;
    const matchesPriority = priorityFilter === 'All' || accidentCase.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesSeverity && matchesPriority;
  });

  const handleReportAccident = () => {
    navigate('/maintenance/accidents/report');
  };

  const handleViewCase = (caseId: string) => {
    navigate(`/maintenance/accidents/${caseId}`);
  };

  const handleViewPhotos = (caseId: string) => {
    navigate(`/maintenance/accidents/${caseId}?tab=photos`);
  };

  const handleEditCase = (caseId: string) => {
    navigate(`/maintenance/accidents/${caseId}/edit`);
  };

  return (
    <div className="p-3 sm:p-4 lg:p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 gap-3">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Accident Case Management</h1>
          <p className="text-sm sm:text-base text-gray-600">Manage vehicle accident reports and repair workflows</p>
        </div>
        <button
          onClick={handleReportAccident}
          className="bg-red-600 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 hover:bg-red-700 text-sm sm:text-base h-10 sm:h-auto"
        >
          <Plus className="w-4 h-4" />
          <span className="hidden sm:inline">Report Accident</span>
          <span className="sm:hidden">Report</span>
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border p-3 sm:p-4 mb-4 sm:mb-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search vehicles, drivers, locations..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* Status Filter */}
          <select
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="All">All Statuses</option>
            <option value="Reported">Reported</option>
            <option value="Assessed">Assessed</option>
            <option value="Quoted">Quoted</option>
            <option value="Approved">Approved</option>
            <option value="In Repair">In Repair</option>
            <option value="Completed">Completed</option>
            <option value="Closed">Closed</option>
          </select>

          {/* Severity Filter */}
          <select
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={severityFilter}
            onChange={(e) => setSeverityFilter(e.target.value)}
          >
            <option value="All">All Severities</option>
            <option value="Minor">Minor</option>
            <option value="Moderate">Moderate</option>
            <option value="Major">Major</option>
            <option value="Total Loss">Total Loss</option>
          </select>

          {/* Priority Filter */}
          <select
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={priorityFilter}
            onChange={(e) => setPriorityFilter(e.target.value)}
          >
            <option value="All">All Priorities</option>
            <option value="Critical">Critical</option>
            <option value="High">High</option>
            <option value="Medium">Medium</option>
            <option value="Low">Low</option>
          </select>
        </div>
      </div>

      {/* Results Summary */}
      <div className="mb-4">
        <p className="text-gray-600">
          Showing {filteredCases.length} of {mockCases.length} accident cases
        </p>
      </div>

      {/* Cases Table */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Case ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Vehicle
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Driver
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Accident Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Severity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Priority
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Est. Cost
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredCases.map((accidentCase) => (
                <tr key={accidentCase.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {accidentCase.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{accidentCase.vehicleReg}</div>
                      <div className="text-sm text-gray-500">{accidentCase.vehicleMake} {accidentCase.vehicleModel}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4 text-gray-400" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">{accidentCase.driverName}</div>
                        <div className="text-sm text-gray-500">{accidentCase.driverContact}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-900">
                        {new Date(accidentCase.accidentDate).toLocaleDateString()}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(accidentCase.severity)}`}>
                      {accidentCase.severity}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(accidentCase.status)}
                      <span className="text-sm text-gray-900">{accidentCase.status}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(accidentCase.priority)}`}>
                      {accidentCase.priority}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {accidentCase.estimatedCost ? `R ${accidentCase.estimatedCost.toLocaleString()}` : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <button 
                        onClick={() => handleViewPhotos(accidentCase.id)}
                        className="text-blue-600 hover:text-blue-900" 
                        title="View Photos"
                      >
                        <Camera className="w-4 h-4" />
                      </button>
                      <button 
                        onClick={() => handleViewCase(accidentCase.id)}
                        className="text-blue-600 hover:text-blue-900" 
                        title="View Details"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button 
                        onClick={() => handleEditCase(accidentCase.id)}
                        className="text-gray-600 hover:text-gray-900" 
                        title="Edit Case"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Empty State */}
      {filteredCases.length === 0 && (
        <div className="text-center py-12">
          <AlertCircle className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No accident cases found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search criteria or report a new accident case.
          </p>
        </div>
      )}
    </div>
  );
};

export default AccidentCaseManagementPage;
