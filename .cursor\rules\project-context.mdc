---
alwaysApply: true
---
# RT46-2026 Fleet Management System - Project Context

This is the RT46-2026 Vehicle Fleet Management System for South African government institutions. 

## Core Technology Stack (CONFIRMED)
- **Backend**: Python 3.11+ with FastAPI framework
- **Database ORM**: SQLAlchemy Core (NOT Prisma - removed for fine-grained SQL control)
- **Data Validation**: Pydantic 2.0+
- **Primary Database**: PostgreSQL 15
- **Document Database**: Firestore
- **Cache**: Redis
- **Message Queue**: RabbitMQ
- **Frontend**: React.js 18+ with TypeScript
- **Mobile**: React Native
- **Cloud Platform**: Google Cloud Platform (GCP)
- **Container Orchestration**: Google Kubernetes Engine (GKE)

## Key Project Files
- [PRD.md](mdc:business_docs/PRD.md) - Product Requirements Document
- [design_specification.md](mdc:design_specification.md) - Technical Design Specification  
- [rules.md](mdc:rules.md) - Development Rules and Best Practices

## Project Scope
- 60+ government institutions
- 10,000+ concurrent users
- Fleet management including vehicles, maintenance, work orders, vendors, fuel management
- POPIA compliance for South African data protection
- BBBEE transformation requirements
- Integration with government systems (RTMC, SARS, banking)

## Critical Notes
- This is a government project requiring high security and compliance standards
- Backend must be Python-based (previously incorrectly specified as Node.js)
- No Prisma ORM - use SQLAlchemy Core for database operations
- Follow South African government procurement and regulatory requirements
