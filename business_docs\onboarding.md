
# 📦 Fleet Manager Onboarding Wireframe Prompts (Markdown Format)

This file contains detailed screen-by-screen prompts to generate Figma-style wireframes for the onboarding module of a government fleet management system.

---

## 1. **New Customer Profile Form**
```
Design a wireframe screen titled "New Customer Profile Form" for a Fleet Manager onboarding a new government department into a fleet management system. The form should be simple, gray wireframe-style. Include:

- Page title at top
- Input fields: Department Name, Department Type (dropdown), Contact Person Name, Email, Phone Number
- Dropdown for SLA Level (Standard, Priority, Custom)
- Text area for Additional Notes
- Submit and Cancel buttons at the bottom

Use a clean, centered card layout with field labels on the left and inputs on the right.
```

---

## 2. **Organizational Hierarchy Builder**
```
Create a wireframe titled "Organizational Hierarchy Builder" for defining a new customer’s structure. The user is a Fleet Manager.

- Page title at top
- Left section: Tree view showing roles like Department Head > Supervisor > Officer
- Right section: Form with Add Role, Role Name input, and Assign User dropdown
- Buttons: Add Role, Save Changes, Cancel
- Use collapsible panels for tree view

Use clean gray boxes and clearly separated sections.
```

---

## 3. **Fleet Data Upload Page**
```
Design a screen called "Fleet Data Upload Page" for uploading vehicle and driver data.

- Top section with instructions
- Drag-and-drop area for CSV/Excel upload
- Button for downloading sample template
- Table preview below to show uploaded data
- Validation messages under each column (e.g., Missing Plate Number)

Use a vertical layout, with clear labels and visual hierarchy.
```

---

## 4. **Document Upload Section**
```
Create a wireframe titled "Document Upload Section" for uploading customer documents.

- Left side: List of required documents (Ownership Certificate, Location Map, Insurance Proof)
- Right side: Drag-and-drop upload area per document
- Each row should have status: Not Uploaded / Uploaded / Rejected
- Show a “Continue” button at the bottom

Use card-style file boxes and light gray tones for structure.
```

---

## 5. **User Roles & Permissions Manager**
```
Design a screen titled "User Roles & Permissions Manager".

- Table view: Columns for Username, Email, Role (dropdown), Status
- Top bar with "Add New User" button
- Modal form for adding user: Name, Email, Assign Role
- Option to delete or disable users

Layout should be clean and administrative in tone.
```

---

## 6. **Merchant Linker**
```
Design a screen called "Merchant Linker".

- Search bar at top to search merchants
- Table of available merchants: Name, Region, Specialty, Status
- Multi-select checkbox to assign merchants to customer
- Right panel shows “Linked Merchants” with option to remove

Use two-column layout: Available and Linked Merchants
```

---

## 7. **Approval Workflow Editor**
```
Create a wireframe titled "Approval Workflow Editor".

- Flowchart builder with nodes: Request → Supervisor → Approver → Complete
- Each node is editable: Role, Spend Limit, SLA Time
- Toolbar on top: Add Node, Save Workflow, Reset
- Show zoomable canvas or diagram view

Use a modular layout with blocks connected by arrows.
```

---

## 8. **Onboarding Checklist / Progress View**
```
Design a screen titled "Onboarding Progress Checklist".

- Vertical checklist of steps: Profile, Data Upload, Documents, Roles, Workflow, Merchant Link, Test
- Each item has status: Pending / Completed / Error
- Progress bar at top
- Button: View Configuration Summary

Use a stepper-like layout with green ticks for completed items.
```

---

## 9. **Test Simulation Panel**
```
Create a screen titled "Test Simulation Panel".

- Dropdown to select Test Scenario (e.g., Work Order, Inspection Approval)
- Button: Run Test
- Below: Result log area showing actions simulated, time taken, status
- Summary status: Pass / Fail

Use clear panels and neutral gray test output sections.
```

---

## 10. **Activation Page**
```
Design a wireframe titled "Activation Page".

- Text section: "Ready to Go Live?"
- Details recap: Department Name, Users Created, Data Uploaded
- Button: Activate Now (with confirmation modal)
- Once activated: show success banner and Go to Dashboard button

Keep layout centered, with visual cues for readiness.
```

---

## 11. **First Login Wizard**
```
Create a "First Login Wizard" overlay wireframe.

- Step-by-step guide: Welcome → Fleet Overview → Job Tracking → Support Access
- Each step: Title, description, visual/graphic placeholder
- Navigation buttons: Back, Next, Finish

Use a modal-style floating wizard with progress indicator (Step 1 of 4).
```

---

## 12. **Usage Dashboard**
```
Design a dashboard screen titled "Customer Usage Overview".

- Top: Metrics — Total Logins, Active Users, Jobs Created
- Middle: Line chart of engagement over time
- Bottom: Table of recent user activity
- Sidebar: Department name, date onboarded

Use admin dashboard layout — 3 sections vertically stacked.
```

---

## 13. **Alerts & Tickets Panel**
```
Design a screen called "Alerts & Tickets Panel".

- Alert list: e.g., "No Login in 3 Days", "Merchant Not Linked"
- Ticket table: ID, Issue Summary, Status, Assigned Agent
- Filters for severity (Info, Warning, Critical)

Use badge indicators and red/yellow flags for alerts.
```

---

## 14. **Quick Edit Panel**
```
Create a wireframe titled "Quick Edit Panel".

- Search field to find user, role, merchant, or setting
- Editable cards for User Role, Approval Limit, Merchant Link
- Save & Apply Changes button

Use a utility-style layout with collapsible edit sections.
```

---

## 15. **Handover Summary Page**
```
Design a screen titled "Handover Summary Page".

- Summary: Onboarding Completed Date, Department, Assigned Support Officer
- Button to Export Configuration Summary (PDF/Excel)
- Button: Mark as Complete

Include success icon and callout for customer satisfaction.
```

---

## 16. **Feedback Survey Modal**
```
Create a wireframe titled "Feedback Survey Modal".

- Modal with 3 questions:
  1. How satisfied were you with the onboarding process?
  2. What could we improve?
  3. Would you recommend this platform?
- 5-star rating input and text areas
- Submit and Skip buttons

Keep it clean and centered.
```
