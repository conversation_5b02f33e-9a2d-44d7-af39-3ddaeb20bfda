import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import { store } from './store';
import Layout from './components/layout/Layout';
import ProtectedRoute from './components/auth/ProtectedRoute';
import LoginPage from './features/auth/pages/LoginPage';
import SignUpPage from './features/auth/pages/SignUpPage';
import ForgotPasswordPage from './features/auth/pages/ForgotPasswordPage';
import DashboardPage from './features/dashboard/pages/DashboardPage';
import { VehicleListPage } from './features/vehicles/pages/VehicleListPage';
import WorkOrderListPage from './features/workorders/pages/WorkOrderListPage';
import { VehicleDetailPage } from './features/vehicles/pages/VehicleDetailPage';
import { AddVehiclePage } from './features/vehicles/pages/AddVehiclePage';
import CreateWorkOrderPage from './features/workorders/pages/CreateWorkOrderPage';
import ScheduleServicePage from './features/maintenance/pages/ScheduleServicePage';
import WorkOrderDetailPage from './features/workorders/pages/WorkOrderDetailPage';
import MerchantWorkOrderDetailPage from './features/merchant/pages/WorkOrderDetailPage';
import ToastContainer from './components/ui/ToastContainer';
import { EditVehiclePage } from './features/vehicles/pages/EditVehiclePage';
import SettingsPage from './features/settings/pages/SettingsPage';
import MaintenanceRequestListPage from './features/maintenance/pages/MaintenanceRequestListPage';
import QuotationComparisonPage from './features/maintenance/pages/QuotationComparisonPage';
import AccidentCaseManagementPage from './features/maintenance/pages/AccidentCaseManagementPage';
import ReportAccidentPage from './features/maintenance/pages/ReportAccidentPage';
import AccidentCaseDetailPage from './features/maintenance/pages/AccidentCaseDetailPage';
import VendorListPage from './features/vendors/pages/VendorListPage';
import VendorDetailPage from './features/vendors/pages/VendorDetailPage';
import AddVendorPage from './features/vendors/pages/AddVendorPage';
import EditVendorPage from './features/vendors/pages/EditVendorPage';
import ReportsPage from './features/reports/pages/ReportsPage';
import TransportOfficerDashboard from './features/transport/pages/TransportOfficerDashboard';
import MaintenanceSchedulingPage from './features/transport/pages/MaintenanceSchedulingPage';
import TripLogViewerPage from './features/transport/pages/TripLogViewerPage';
import BookingCalendarPage from './features/transport/pages/BookingCalendarPage';
import MaintenanceRequestPage from './features/transport/pages/MaintenanceRequestPage';
import UnauthorizedPage from './features/auth/pages/UnauthorizedPage';
import DriverDashboard from './features/driver/pages/DriverDashboard';
import VehicleBookingPage from './features/driver/pages/VehicleBookingPage';
import PreTripCheckPage from './features/driver/pages/PreTripCheckPage';
import ReportIssuePage from './features/driver/pages/ReportIssuePage';
import FuelLogPage from './features/driver/pages/FuelLogPage';
import TripHistoryPage from './features/driver/pages/TripHistoryPage';
import DriverModals from './components/modals/DriverModals';
// Finance Officer Routes
import FinanceDashboard from './features/finance/pages/FinanceDashboard';
import InvoiceDashboard from './features/finance/pages/InvoiceDashboard';
import InvoiceDetailView from './features/finance/pages/InvoiceDetailView';
import PaymentStatusTracker from './features/finance/pages/PaymentStatusTracker';
import BudgetManagement from './features/finance/pages/BudgetManagement';
// Merchant/Vendor Routes
import MerchantDashboard from './features/merchant/pages/MerchantDashboard';
import WorkOrderQueue from './features/merchant/pages/WorkOrderQueue';
import QuoteSubmissionPage from './features/merchant/pages/QuoteSubmissionPage';
import JobCardView from './features/merchant/pages/JobCardView';
import InvoiceManagement from './features/merchant/pages/InvoiceManagement';
import InvoiceSubmissionPage from './features/merchant/pages/InvoiceSubmissionPage';
import InspectorDashboard from './features/inspector/pages/InspectorDashboard';
import InspectionChecklist from './features/inspector/pages/InspectionChecklist';
import MerchantPaymentStatusTracker from './features/merchant/pages/PaymentStatus';
import InspectionReportViewer from './features/inspector/pages/InspectionReportViewer';
import InspectionSchedule from './features/inspector/pages/InspectionSchedule';
import DepartmentOnboardingPage from './features/admin/pages/DepartmentOnboardingPage';
import PendingDepartmentApplications from './features/admin/pages/PendingDepartmentApplications';
import OnboardingSuccessPage from './features/admin/pages/OnboardingSuccessPage';
import { LandingPage } from './features/public';
import LiveTrackingMap from './features/tracking/pages/LiveTrackingMap';

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <ToastContainer />
          <Routes>
            <Route path="/" element={<LandingPage />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/signup" element={<SignUpPage />} />
            <Route path="/forgot-password" element={<ForgotPasswordPage />} />
            <Route path="/onboard" element={<DepartmentOnboardingPage />} />
            <Route path="/onboard/success" element={<OnboardingSuccessPage />} />
            <Route path="/unauthorized" element={<UnauthorizedPage />} />
            <Route path="/*" element={
              <ProtectedRoute>
                <Layout>
                  <Routes>
                    <Route path="/dashboard" element={<DashboardPage />} />
                    
                    {/* Common Routes - All authenticated users */}
                    <Route path="/reports" element={<ReportsPage />} />
                    <Route path="/settings" element={<SettingsPage />} />
                    
                    {/* Fleet Manager & Admin Routes */}
                    <Route path="/vehicles" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin']}>
                        <VehicleListPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/vehicles/add" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin']}>
                        <AddVehiclePage />
                      </ProtectedRoute>
                    } />
                    <Route path="/vehicles/:id" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin', 'transport_officer']}>
                        <VehicleDetailPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/vehicles/:id/edit" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin']}>
                        <EditVehiclePage />
                      </ProtectedRoute>
                    } />
                    <Route path="/vehicles/:vehicleId/schedule-service" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin', 'transport_officer']}>
                        <ScheduleServicePage />
                      </ProtectedRoute>
                    } />
                    
                    {/* Work Orders - Fleet Manager & Admin */}
                    <Route path="/work-orders" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin']}>
                        <WorkOrderListPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/work-orders/create" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin']}>
                        <CreateWorkOrderPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/work-orders/:id" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin', 'transport_officer']}>
                        <WorkOrderDetailPage />
                      </ProtectedRoute>
                    } />
                    
                    {/* Maintenance Routes */}
                    <Route path="/maintenance/schedule" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin', 'transport_officer']}>
                        <ScheduleServicePage />
                      </ProtectedRoute>
                    } />
                    <Route path="/maintenance/requests" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin', 'transport_officer']}>
                        <MaintenanceRequestListPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/maintenance/requests/:requestId/quotes" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin']}>
                        <QuotationComparisonPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/maintenance/accidents" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin', 'transport_officer']}>
                        <AccidentCaseManagementPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/maintenance/accidents/report" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin', 'transport_officer']}>
                        <ReportAccidentPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/maintenance/accidents/:id" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin', 'transport_officer']}>
                        <AccidentCaseDetailPage />
                      </ProtectedRoute>
                    } />
                    
                    {/* Vendor Management - Fleet Manager & Admin */}
                    <Route path="/vendors" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin']}>
                        <VendorListPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/vendors/add" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin']}>
                        <AddVendorPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/vendors/:id" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin']}>
                        <VendorDetailPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/vendors/:id/edit" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin']}>
                        <EditVendorPage />
                      </ProtectedRoute>
                    } />
                    
                    {/* Live Tracking - Fleet Manager & Transport Officer */}
                    <Route path="/tracking" element={
                      <ProtectedRoute requiredRoles={['fleet_manager', 'admin', 'transport_officer']}>
                        <LiveTrackingMap />
                      </ProtectedRoute>
                    } />
                    
                    {/* Transport Officer Specific Routes */}
                    <Route path="/transport/dashboard" element={
                      <ProtectedRoute requiredRoles={['transport_officer', 'admin']}>
                        <TransportOfficerDashboard />
                      </ProtectedRoute>
                    } />
                    <Route path="/transport/maintenance/schedule" element={
                      <ProtectedRoute requiredRoles={['transport_officer', 'admin']}>
                        <MaintenanceSchedulingPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/transport/maintenance/request" element={
                      <ProtectedRoute requiredRoles={['transport_officer', 'admin']}>
                        <MaintenanceRequestPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/transport/trip-logs" element={
                      <ProtectedRoute requiredRoles={['transport_officer', 'admin']}>
                        <TripLogViewerPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/transport/booking-calendar" element={
                      <ProtectedRoute requiredRoles={['transport_officer', 'admin']}>
                        <BookingCalendarPage />
                      </ProtectedRoute>
                    } />
                    
                    {/* Driver Specific Routes */}
                    <Route path="/driver/dashboard" element={
                      <ProtectedRoute requiredRoles={['driver', 'admin']}>
                        <DriverDashboard />
                      </ProtectedRoute>
                    } />
                    <Route path="/driver/book-vehicle" element={
                      <ProtectedRoute requiredRoles={['driver', 'admin']}>
                        <VehicleBookingPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/driver/pre-trip-check" element={
                      <ProtectedRoute requiredRoles={['driver', 'admin']}>
                        <PreTripCheckPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/driver/report-issue" element={
                      <ProtectedRoute requiredRoles={['driver', 'admin']}>
                        <ReportIssuePage />
                      </ProtectedRoute>
                    } />
                    <Route path="/driver/fuel-log" element={
                      <ProtectedRoute requiredRoles={['driver', 'admin']}>
                        <FuelLogPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/driver/trip-history" element={
                      <ProtectedRoute requiredRoles={['driver', 'admin']}>
                        <TripHistoryPage />
                      </ProtectedRoute>
                    } />
                    {/* Finance Officer Routes */}
                    <Route path="/finance/dashboard" element={
                      <ProtectedRoute requiredRoles={['finance_officer', 'admin']}>
                        <FinanceDashboard />
                      </ProtectedRoute>
                    } />
                    <Route path="/finance/invoices" element={
                      <ProtectedRoute requiredRoles={['finance_officer', 'admin']}>
                        <InvoiceDashboard />
                      </ProtectedRoute>
                    } />
                    <Route path="/finance/invoices/:id" element={
                      <ProtectedRoute requiredRoles={['finance_officer', 'admin']}>
                        <InvoiceDetailView />
                      </ProtectedRoute>
                    } />
                    <Route path="/finance/payments" element={
                      <ProtectedRoute requiredRoles={['finance_officer', 'admin']}>
                        <PaymentStatusTracker />
                      </ProtectedRoute>
                    } />
                    <Route path="/finance/budget" element={
                      <ProtectedRoute requiredRoles={['finance_officer', 'admin']}>
                        <BudgetManagement />
                      </ProtectedRoute>
                    } />
                    {/* Merchant/Vendor Routes */}
                    <Route path="/merchant/dashboard" element={
                      <ProtectedRoute requiredRoles={['merchant', 'admin']}>
                        <MerchantDashboard />
                      </ProtectedRoute>
                    } />
                    <Route path="/merchant/work-orders" element={
                      <ProtectedRoute requiredRoles={['merchant', 'admin']}>
                        <WorkOrderQueue />
                      </ProtectedRoute>
                    } />
                    <Route path="/merchant/work-orders/:id" element={
                      <ProtectedRoute requiredRoles={['merchant', 'admin']}>
                        <MerchantWorkOrderDetailPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/merchant/quotes" element={
                      <ProtectedRoute requiredRoles={['merchant', 'admin']}>
                        <QuoteSubmissionPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/merchant/job-cards/:id" element={
                      <ProtectedRoute requiredRoles={['merchant', 'admin']}>
                        <JobCardView />
                      </ProtectedRoute>
                    } />
                    <Route path="/merchant/invoices" element={
                      <ProtectedRoute requiredRoles={['merchant', 'admin']}>
                        <InvoiceManagement />
                      </ProtectedRoute>
                    } />
                    <Route path="/merchant/invoices/submit" element={
                      <ProtectedRoute requiredRoles={['merchant', 'admin']}>
                        <InvoiceSubmissionPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/merchant/payments" element={
                      <ProtectedRoute requiredRoles={['merchant', 'admin']}>
                        <MerchantPaymentStatusTracker />
                      </ProtectedRoute>
                    } />
                    {/* Inspector Routes */}
                    <Route path="/inspector/dashboard" element={
                      <ProtectedRoute requiredRoles={['inspector', 'admin']}>
                        <InspectorDashboard />
                      </ProtectedRoute>
                    } />
                    <Route path="/inspector/checklist/:id" element={
                      <ProtectedRoute requiredRoles={['inspector', 'admin']}>
                        <InspectionChecklist />
                      </ProtectedRoute>
                    } />
                    <Route path="/inspector/reports" element={
                      <ProtectedRoute requiredRoles={['inspector', 'admin']}>
                        <InspectionReportViewer />
                      </ProtectedRoute>
                    } />
                    <Route path="/inspector/schedule" element={
                      <ProtectedRoute requiredRoles={['inspector', 'admin']}>
                        <InspectionSchedule />
                      </ProtectedRoute>
                    } />
                    <Route path="/admin/departments/pending" element={
                      <ProtectedRoute requiredRoles={['admin']}>
                        <PendingDepartmentApplications />
                      </ProtectedRoute>
                    } />
                  </Routes>
                </Layout>
              </ProtectedRoute>
            } />
          </Routes>
          <ToastContainer />
          <DriverModals />
        </div>
      </Router>
    </Provider>
  );
};

export default App;
