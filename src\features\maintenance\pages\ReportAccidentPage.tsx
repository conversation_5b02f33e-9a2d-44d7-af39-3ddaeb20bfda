import React, { useState } from 'react';
import { ArrowLeft, Camera, Upload, MapPin, User, Calendar, AlertTriangle, Save, Send } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface AccidentFormData {
  vehicleId: string;
  driverName: string;
  driverContact: string;
  accidentDate: string;
  accidentTime: string;
  location: string;
  description: string;
  severity: 'Minor' | 'Moderate' | 'Major' | 'Total Loss';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  injuriesReported: boolean;
  policeReported: boolean;
  policeReportNumber?: string;
  thirdPartyInvolved: boolean;
  thirdPartyDetails?: string;
  weatherConditions: string;
  roadConditions: string;
  photos: File[];
}

const ReportAccidentPage: React.FC = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<AccidentFormData>({
    vehicleId: '',
    driverName: '',
    driverContact: '',
    accidentDate: '',
    accidentTime: '',
    location: '',
    description: '',
    severity: 'Minor',
    priority: 'Medium',
    injuriesReported: false,
    policeReported: false,
    thirdPartyInvolved: false,
    weatherConditions: 'Clear',
    roadConditions: 'Dry',
    photos: []
  });

  const vehicles = [
    { id: 'V-001', reg: 'GP 123 ABC', make: 'Toyota', model: 'Hilux' },
    { id: 'V-002', reg: 'GP 456 DEF', make: 'Ford', model: 'Ranger' },
    { id: 'V-003', reg: 'GP 789 GHI', make: 'Isuzu', model: 'KB' }
  ];

  const handleInputChange = (field: keyof AccidentFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setFormData(prev => ({ ...prev, photos: [...prev.photos, ...files] }));
  };

  const removePhoto = (index: number) => {
    setFormData(prev => ({
      ...prev,
      photos: prev.photos.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (action: 'draft' | 'submit') => {
    setIsSubmitting(true);
    try {
      console.log('Submitting accident report:', { ...formData, status: action });
      await new Promise(resolve => setTimeout(resolve, 1000));
      navigate('/maintenance/accidents', {
        state: { message: `Accident report has been ${action === 'draft' ? 'saved as draft' : 'submitted successfully'}.` }
      });
    } catch (error) {
      console.error('Error submitting accident report:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <button 
          onClick={() => navigate('/maintenance/accidents')}
          className="p-2 hover:bg-gray-100 rounded-lg"
        >
          <ArrowLeft className="w-5 h-5" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Report Accident</h1>
          <p className="text-gray-600">Submit a new vehicle accident report</p>
        </div>
      </div>

      <form className="space-y-6">
        {/* Vehicle Information */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <User className="w-5 h-5" />
            Vehicle & Driver Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Vehicle</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                value={formData.vehicleId}
                onChange={(e) => handleInputChange('vehicleId', e.target.value)}
                required
              >
                <option value="">Select Vehicle</option>
                {vehicles.map(vehicle => (
                  <option key={vehicle.id} value={vehicle.id}>
                    {vehicle.reg} - {vehicle.make} {vehicle.model}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Driver Name</label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                value={formData.driverName}
                onChange={(e) => handleInputChange('driverName', e.target.value)}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Driver Contact</label>
              <input
                type="tel"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                value={formData.driverContact}
                onChange={(e) => handleInputChange('driverContact', e.target.value)}
                required
              />
            </div>
          </div>
        </div>

        {/* Accident Details */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <AlertTriangle className="w-5 h-5" />
            Accident Details
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Date</label>
              <input
                type="date"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                value={formData.accidentDate}
                onChange={(e) => handleInputChange('accidentDate', e.target.value)}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Time</label>
              <input
                type="time"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                value={formData.accidentTime}
                onChange={(e) => handleInputChange('accidentTime', e.target.value)}
                required
              />
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="Street address, intersection, or landmark"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Severity</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                value={formData.severity}
                onChange={(e) => handleInputChange('severity', e.target.value)}
              >
                <option value="Minor">Minor</option>
                <option value="Moderate">Moderate</option>
                <option value="Major">Major</option>
                <option value="Total Loss">Total Loss</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                value={formData.priority}
                onChange={(e) => handleInputChange('priority', e.target.value)}
              >
                <option value="Low">Low</option>
                <option value="Medium">Medium</option>
                <option value="High">High</option>
                <option value="Critical">Critical</option>
              </select>
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
              <textarea
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="Describe what happened, damage sustained, and any other relevant details"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                required
              />
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Additional Information</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  checked={formData.injuriesReported}
                  onChange={(e) => handleInputChange('injuriesReported', e.target.checked)}
                />
                <span className="ml-2 text-sm text-gray-700">Injuries reported</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  checked={formData.policeReported}
                  onChange={(e) => handleInputChange('policeReported', e.target.checked)}
                />
                <span className="ml-2 text-sm text-gray-700">Police reported</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  checked={formData.thirdPartyInvolved}
                  onChange={(e) => handleInputChange('thirdPartyInvolved', e.target.checked)}
                />
                <span className="ml-2 text-sm text-gray-700">Third party involved</span>
              </label>
            </div>
          </div>
        </div>

        {/* Photo Upload */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <Camera className="w-5 h-5" />
            Photos ({formData.photos.length})
          </h3>
          <div className="space-y-4">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handlePhotoUpload}
                className="hidden"
                id="photo-upload"
              />
              <label htmlFor="photo-upload" className="cursor-pointer">
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-2 text-sm text-gray-600">Click to upload photos</p>
                <p className="text-xs text-gray-500">PNG, JPG up to 10MB each</p>
              </label>
            </div>
            {formData.photos.length > 0 && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {formData.photos.map((photo, index) => (
                  <div key={index} className="relative">
                    <img
                      src={URL.createObjectURL(photo)}
                      alt={`Accident photo ${index + 1}`}
                      className="w-full h-24 object-cover rounded-lg"
                    />
                    <button
                      type="button"
                      onClick={() => removePhoto(index)}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => navigate('/maintenance/accidents')}
            className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-2 rounded-lg flex items-center gap-2"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={() => handleSubmit('draft')}
            disabled={isSubmitting}
            className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg flex items-center gap-2 disabled:opacity-50"
          >
            <Save className="w-4 h-4" />
            Save Draft
          </button>
          <button
            type="button"
            onClick={() => handleSubmit('submit')}
            disabled={isSubmitting}
            className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg flex items-center gap-2 disabled:opacity-50"
          >
            <Send className="w-4 h-4" />
            Submit Report
          </button>
        </div>
      </form>
    </div>
  );
};

export default ReportAccidentPage;