import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface ApiError {
  id: string;
  message: string;
  code?: string;
  status?: number;
  timestamp: number;
  endpoint?: string;
  retryable?: boolean;
  details?: Record<string, any>;
}

interface ErrorState {
  errors: ApiError[];
  globalError: ApiError | null;
  networkStatus: 'online' | 'offline' | 'slow';
  retryQueue: string[];
}

const initialState: ErrorState = {
  errors: [],
  globalError: null,
  networkStatus: 'online',
  retryQueue: []
};

const errorSlice = createSlice({
  name: 'error',
  initialState,
  reducers: {
    addError: (state, action: PayloadAction<Omit<ApiError, 'id' | 'timestamp'>>) => {
      const error: ApiError = {
        ...action.payload,
        id: `error_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`,
        timestamp: Date.now()
      };
      state.errors.push(error);
      
      // Set as global error if critical
      if (action.payload.status && action.payload.status >= 500) {
        state.globalError = error;
      }
    },
    
    removeError: (state, action: PayloadAction<string>) => {
      state.errors = state.errors.filter(error => error.id !== action.payload);
      if (state.globalError?.id === action.payload) {
        state.globalError = null;
      }
    },
    
    clearAllErrors: (state) => {
      state.errors = [];
      state.globalError = null;
    },
    
    setNetworkStatus: (state, action: PayloadAction<'online' | 'offline' | 'slow'>) => {
      state.networkStatus = action.payload;
    },
    
    addToRetryQueue: (state, action: PayloadAction<string>) => {
      if (!state.retryQueue.includes(action.payload)) {
        state.retryQueue.push(action.payload);
      }
    },
    
    removeFromRetryQueue: (state, action: PayloadAction<string>) => {
      state.retryQueue = state.retryQueue.filter(id => id !== action.payload);
    }
  }
});

export const {
  addError,
  removeError,
  clearAllErrors,
  setNetworkStatus,
  addToRetryQueue,
  removeFromRetryQueue
} = errorSlice.actions;

export default errorSlice.reducer;