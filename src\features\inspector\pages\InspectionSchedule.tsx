import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useAppDispatch } from '@/hooks/redux';
import { addNotification } from '@/store/slices/uiSlice';
import ScheduleInspectionForm from '../components/ScheduleInspectionForm';
import InspectionDetailView from '../components/InspectionDetailView';
import EditInspectionForm from '../components/EditInspectionForm';
import { 
  Calendar,
  ChevronLeft,
  ChevronRight,
  Plus,
  Clock,
  MapPin,
  Car,
  Building,
  User,
  Phone,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import Modal from '@/components/ui/Modal';
import ConfirmDialog from '@/components/ui/ConfirmDialog';

interface ScheduledInspection {
  id: string;
  type: 'vehicle' | 'merchant';
  subject: {
    name: string;
    registration?: string;
    contact<PERSON>erson?: string;
  };
  date: string;
  time: string;
  duration: number;
  location: string;
  inspector: string;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  status: 'Scheduled' | 'Confirmed' | 'In Progress' | 'Completed' | 'Cancelled' | 'Overdue';
  reason: string;
  phone: string;
}

const InspectionSchedule: React.FC = () => {
  const dispatch = useAppDispatch();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'day'>('month');
  const [showScheduleForm, setShowScheduleForm] = useState(false);
  
  // Make inspections state dynamic
  const [inspections, setInspections] = useState<ScheduledInspection[]>([
    {
      id: '1',
      type: 'vehicle',
      subject: {
        name: 'Toyota Hilux',
        registration: 'GP123ABC'
      },
      date: '2025-01-30',
      time: '09:00',
      duration: 45,
      location: 'Department of Health, Pretoria',
      inspector: 'John Doe',
      priority: 'High',
      status: 'Scheduled',
      reason: 'Annual Safety Inspection',
      phone: '************'
    },
    {
      id: '2',
      type: 'merchant',
      subject: {
        name: 'AutoFix Workshop',
        contactPerson: 'John Smith'
      },
      date: '2025-01-30',
      time: '11:30',
      duration: 90,
      location: 'AutoFix Workshop, Johannesburg',
      inspector: 'Jane Smith',
      priority: 'Medium',
      status: 'Confirmed',
      reason: 'Quarterly Facility Inspection',
      phone: '************'
    },
    {
      id: '3',
      type: 'vehicle',
      subject: {
        name: 'Ford Ranger',
        registration: 'GP456DEF'
      },
      date: new Date().toISOString().split('T')[0], // Today
      time: '14:00',
      duration: 60,
      location: 'City Hall, Johannesburg',
      inspector: 'Mike Johnson',
      priority: 'Medium',
      status: 'In Progress',
      reason: 'Routine Safety Check',
      phone: '************'
    },
    {
      id: '4',
      type: 'merchant',
      subject: {
        name: 'QuickFix Garage',
        contactPerson: 'Sarah Wilson'
      },
      date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Tomorrow
      time: '10:00',
      duration: 120,
      location: 'QuickFix Garage, Pretoria',
      inspector: 'John Doe',
      priority: 'High',
      status: 'Scheduled',
      reason: 'Compliance Audit',
      phone: '************'
    },
    {
      id: '5',
      type: 'vehicle',
      subject: {
        name: 'BMW X3',
        registration: 'GP789GHI'
      },
      date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Yesterday (overdue)
      time: '16:00',
      duration: 45,
      location: 'BMW Service Center, Sandton',
      inspector: 'Jane Smith',
      priority: 'Critical',
      status: 'In Progress',
      reason: 'Emergency Safety Check',
      phone: '************'
    }
  ]);
  const [editingInspection, setEditingInspection] = useState<ScheduledInspection | null>(null);
  const [viewingInspection, setViewingInspection] = useState<ScheduledInspection | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [inspectionToDelete, setInspectionToDelete] = useState<ScheduledInspection | null>(null);

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }
    
    return days;
  };

  const getInspectionsForDate = (date: Date | null) => {
    if (!date) return [];
    const dateString = date.toISOString().split('T')[0];
    return inspections.filter(inspection => inspection.date === dateString);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'In Progress': return 'bg-blue-100 text-blue-800';
      case 'Confirmed': return 'bg-purple-100 text-purple-800';
      case 'Scheduled': return 'bg-gray-100 text-gray-800';
      case 'Cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handlePreviousMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
  };

  const handleNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
  };

  const handleScheduleInspection = () => {
    setShowScheduleForm(true);
  };

  const handleCloseScheduleForm = () => {
    setShowScheduleForm(false);
  };

  const handleSubmitSchedule = (formData: any) => {
    const newInspection: ScheduledInspection = {
      id: Date.now().toString(),
      type: formData.type,
      subject: {
        name: formData.subjectName,
        registration: formData.type === 'vehicle' ? formData.registration : undefined,
        contactPerson: formData.type === 'merchant' ? formData.contactPerson : undefined
      },
      date: formData.date,
      time: formData.time,
      duration: parseInt(formData.duration),
      location: formData.location,
      inspector: formData.inspector || 'Unassigned',
      priority: formData.priority,
      status: 'Scheduled',
      reason: formData.reason || 'Regular Inspection',
      phone: formData.phone || ''
    };

    setInspections(prev => [...prev, newInspection]);
    setShowScheduleForm(false);
    
    dispatch(addNotification({
      type: 'success',
      message: 'Inspection Scheduled',
      details: `New ${formData.type} inspection added for ${formData.date} at ${formData.time}`
    }));
  };

  const handleInspectionClick = (inspection: ScheduledInspection) => {
    dispatch(addNotification({
      type: 'info',
      message: 'Inspection Details',
      details: `Opening details for ${inspection.subject.name}`
    }));
  };

  const handleEditInspection = (inspection: ScheduledInspection) => {
    setEditingInspection(inspection);
  };

  const handleViewInspection = (inspection: ScheduledInspection) => {
    setViewingInspection(inspection);
  };

  const handleDeleteInspection = (inspectionId: string) => {
    const inspection = inspections.find(i => i.id === inspectionId);
    if (inspection) {
      setInspectionToDelete(inspection);
      setShowDeleteConfirm(true);
    }
  };

  const handleConfirmDelete = () => {
    if (inspectionToDelete) {
      setInspections(prev => prev.filter(i => i.id !== inspectionToDelete.id));
      dispatch(addNotification({
        type: 'success',
        message: 'Inspection Deleted',
        details: `Inspection for ${inspectionToDelete.subject.name} has been removed.`
      }));
      setShowDeleteConfirm(false);
      setInspectionToDelete(null);
      setViewingInspection(null);
    }
  };

  const handleUpdateInspection = (updatedInspection: ScheduledInspection) => {
    setInspections(prev => prev.map(i => 
      i.id === updatedInspection.id ? updatedInspection : i
    ));
    setEditingInspection(null);
    dispatch(addNotification({
      type: 'success',
      message: 'Inspection Updated',
      details: `Inspection for ${updatedInspection.subject.name} has been updated.`
    }));
  };

  const handleStatusChange = (inspectionId: string, newStatus: ScheduledInspection['status']) => {
    setInspections(prev => prev.map(i => 
      i.id === inspectionId ? { ...i, status: newStatus } : i
    ));
    const inspection = inspections.find(i => i.id === inspectionId);
    if (inspection) {
      dispatch(addNotification({
        type: 'success',
        message: 'Status Updated',
        details: `${inspection.subject.name} status changed to ${newStatus}.`
      }));
    }
  };

  // Helper functions to categorize inspections
  const getTodaysInspections = () => {
    const today = new Date().toISOString().split('T')[0];
    return inspections.filter(inspection => inspection.date === today);
  };

  const getUpcomingInspections = () => {
    const today = new Date().toISOString().split('T')[0];
    return inspections.filter(inspection => inspection.date > today && inspection.status !== 'Cancelled');
  };

  const getOverdueInspections = () => {
    const today = new Date().toISOString().split('T')[0];
    return inspections.filter(inspection => 
      inspection.date < today && 
      (inspection.status === 'Scheduled' || inspection.status === 'Confirmed')
    );
  };

  const getInProgressInspections = () => {
    return inspections.filter(inspection => inspection.status === 'In Progress');
  };

  const getCompletedInspections = () => {
    return inspections.filter(inspection => inspection.status === 'Completed');
  };

  const days = getDaysInMonth(currentDate);
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  return (
    <div className="container mx-auto px-6 py-8 space-y-6 max-w-7xl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Inspection Schedule</h1>
          <p className="text-gray-600">Manage inspection appointments and calendar</p>
        </div>
        <Button onClick={handleScheduleInspection}>
          <Plus className="h-4 w-4 mr-2" />
          Schedule Inspection
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <Calendar className="h-6 w-6 text-blue-600 mx-auto mb-2" />
              <p className="text-xs font-medium text-gray-600">Today</p>
              <p className="text-lg font-bold text-blue-600">{getTodaysInspections().length}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <Clock className="h-6 w-6 text-yellow-600 mx-auto mb-2" />
              <p className="text-xs font-medium text-gray-600">In Progress</p>
              <p className="text-lg font-bold text-yellow-600">{getInProgressInspections().length}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <Calendar className="h-6 w-6 text-green-600 mx-auto mb-2" />
              <p className="text-xs font-medium text-gray-600">Upcoming</p>
              <p className="text-lg font-bold text-green-600">{getUpcomingInspections().length}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <CheckCircle className="h-6 w-6 text-purple-600 mx-auto mb-2" />
              <p className="text-xs font-medium text-gray-600">Completed</p>
              <p className="text-lg font-bold text-purple-600">{getCompletedInspections().length}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <AlertTriangle className="h-6 w-6 text-red-600 mx-auto mb-2" />
              <p className="text-xs font-medium text-gray-600">Overdue</p>
              <p className="text-lg font-bold text-red-600">{getOverdueInspections().length}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* All Modals */}
      <Modal
        isOpen={showScheduleForm}
        onClose={handleCloseScheduleForm}
        title="Schedule New Inspection"
        size="lg"
      >
        <ScheduleInspectionForm 
          onClose={handleCloseScheduleForm}
          onSubmit={handleSubmitSchedule}
        />
      </Modal>

      <Modal
        isOpen={!!viewingInspection}
        onClose={() => setViewingInspection(null)}
        title="Inspection Details"
        size="lg"
      >
        {viewingInspection && (
          <InspectionDetailView
            inspection={viewingInspection}
            onClose={() => setViewingInspection(null)}
            onEdit={() => {
              setEditingInspection(viewingInspection);
              setViewingInspection(null);
            }}
            onDelete={() => handleDeleteInspection(viewingInspection.id)}
            onStatusChange={(status) => {
              handleStatusChange(viewingInspection.id, status);
              setViewingInspection(null);
            }}
          />
        )}
      </Modal>

      <Modal
        isOpen={!!editingInspection}
        onClose={() => setEditingInspection(null)}
        title="Edit Inspection"
        size="lg"
      >
        {editingInspection && (
          <EditInspectionForm
            inspection={editingInspection}
            onClose={() => setEditingInspection(null)}
            onSave={handleUpdateInspection}
          />
        )}
      </Modal>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showDeleteConfirm}
        onClose={() => {
          setShowDeleteConfirm(false);
          setInspectionToDelete(null);
        }}
        onConfirm={handleConfirmDelete}
        title="Delete Inspection"
        message={`Are you sure you want to delete the inspection for "${inspectionToDelete?.subject.name}"? This action cannot be undone.`}
        type="danger"
        confirmText="Delete"
        cancelText="Cancel"
        icon="delete"
      />

      {/* Calendar Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm" onClick={handlePreviousMonth}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <h2 className="text-xl font-semibold">
                {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
              </h2>
              <Button variant="outline" size="sm" onClick={handleNextMonth}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="flex space-x-2">
              <Button 
                variant={viewMode === 'month' ? 'default' : 'outline'} 
                size="sm"
                onClick={() => setViewMode('month')}
              >
                Month
              </Button>
              <Button 
                variant={viewMode === 'week' ? 'default' : 'outline'} 
                size="sm"
                onClick={() => setViewMode('week')}
              >
                Week
              </Button>
              <Button 
                variant={viewMode === 'day' ? 'default' : 'outline'} 
                size="sm"
                onClick={() => setViewMode('day')}
              >
                Day
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1 mb-4">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
              <div key={day} className="p-2 text-center font-medium text-gray-600 text-sm">
                {day}
              </div>
            ))}
            
            {days.map((day, index) => {
              const dayInspections = getInspectionsForDate(day);
              const isToday = day && day.toDateString() === new Date().toDateString();
              
              return (
                <div
                  key={index}
                  className={`min-h-[80px] p-1 border rounded ${
                    day ? 'bg-white hover:bg-gray-50' : 'bg-gray-100'
                  } ${isToday ? 'ring-2 ring-blue-500' : ''}`}
                >
                  {day && (
                    <>
                      <div className={`text-sm font-medium mb-1 ${
                        isToday ? 'text-blue-600' : 'text-gray-900'
                      }`}>
                        {day.getDate()}
                      </div>
                      
                      <div className="space-y-1">
                        {dayInspections.slice(0, 2).map(inspection => (
                          <div
                            key={inspection.id}
                            className="text-xs p-1 rounded cursor-pointer hover:shadow-sm"
                            style={{
                              backgroundColor: inspection.type === 'vehicle' ? '#dbeafe' : '#f3e8ff'
                            }}
                            onClick={() => handleInspectionClick(inspection)}
                          >
                            <div className="flex items-center space-x-1">
                              {inspection.type === 'vehicle' ? (
                                <Car className="h-3 w-3" />
                              ) : (
                                <Building className="h-3 w-3" />
                              )}
                              <span className="truncate">{inspection.time}</span>
                            </div>
                            <div className="truncate font-medium">
                              {inspection.subject.name}
                            </div>
                          </div>
                        ))}
                        
                        {dayInspections.length > 2 && (
                          <div className="text-xs text-gray-500 text-center">
                            +{dayInspections.length - 2} more
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Today's Inspections */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Today's Inspections ({getTodaysInspections().length})</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {getTodaysInspections().map(inspection => (
              <div
                key={inspection.id}
                className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => handleInspectionClick(inspection)}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    {inspection.type === 'vehicle' ? (
                      <Car className="h-5 w-5 text-blue-600" />
                    ) : (
                      <Building className="h-5 w-5 text-purple-600" />
                    )}
                    <h3 className="font-semibold">{inspection.subject.name}</h3>
                    {inspection.subject.registration && (
                      <Badge variant="outline">{inspection.subject.registration}</Badge>
                    )}
                  </div>
                  
                  <div className="flex space-x-2">
                    <Badge className={getPriorityColor(inspection.priority)}>
                      {inspection.priority}
                    </Badge>
                    <Badge className={getStatusColor(inspection.status)}>
                      {inspection.status}
                    </Badge>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span>{inspection.time} ({inspection.duration} min)</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      <span className="truncate">{inspection.location}</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <span>{inspection.inspector}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <span>{inspection.phone}</span>
                    </div>
                  </div>
                </div>
                
                <div className="mt-3 p-2 bg-gray-50 rounded text-sm">
                  <strong>Reason:</strong> {inspection.reason}
                </div>
                
                {/* Action Buttons */}
                <div className="flex space-x-2 mt-3 pt-3 border-t">
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={(e) => {
                      e.stopPropagation();
                      handleViewInspection(inspection);
                    }}
                    className="flex-1"
                  >
                    View
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditInspection(inspection);
                    }}
                    className="flex-1"
                  >
                    Edit
                  </Button>
                  <Button 
                    size="sm" 
                    variant="destructive" 
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteInspection(inspection.id);
                    }}
                    className="flex-1"
                  >
                    Delete
                  </Button>
                </div>
              </div>
            ))}
            
            {getTodaysInspections().length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No inspections scheduled for today</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Overdue Inspections */}
      {getOverdueInspections().length > 0 && (
        <Card className="border-red-200">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              <span>Overdue Inspections ({getOverdueInspections().length})</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {getOverdueInspections().map(inspection => (
                <div
                  key={inspection.id}
                  className="p-4 border border-red-200 rounded-lg bg-red-50 hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => handleInspectionClick(inspection)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      {inspection.type === 'vehicle' ? (
                        <Car className="h-5 w-5 text-blue-600" />
                      ) : (
                        <Building className="h-5 w-5 text-purple-600" />
                      )}
                      <h3 className="font-semibold">{inspection.subject.name}</h3>
                      {inspection.subject.registration && (
                        <Badge variant="outline">{inspection.subject.registration}</Badge>
                      )}
                    </div>
                    
                    <div className="flex space-x-2">
                      <Badge className={getPriorityColor(inspection.priority)}>
                        {inspection.priority}
                      </Badge>
                      <Badge className="bg-red-100 text-red-800">
                        Overdue
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="text-red-600">
                          {new Date(inspection.date).toLocaleDateString()} at {inspection.time}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-gray-400" />
                        <span className="truncate">{inspection.location}</span>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4 text-gray-400" />
                        <span>{inspection.inspector}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Phone className="h-4 w-4 text-gray-400" />
                        <span>{inspection.phone}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-3 p-2 bg-white rounded text-sm border border-red-200">
                    <strong>Reason:</strong> {inspection.reason}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default InspectionSchedule;













