import React, { useEffect, useRef, useState } from 'react';

interface SimulatedMapProps {
  center: { lat: number; lng: number };
  zoom: number;
  vehicles: Array<{
    id: string;
    registrationNumber: string;
    lat: number;
    lng: number;
    status: 'moving' | 'idling' | 'stopped';
    speed: number;
    driver: string;
    lastUpdate: string;
    department: string;
    heading?: number;
  }>;
  onVehicleClick: (vehicle: any) => void;
}

const SimulatedMap: React.FC<SimulatedMapProps> = ({
  center,
  zoom,
  vehicles,
  onVehicleClick
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [hoveredVehicle, setHoveredVehicle] = useState<string | null>(null);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'moving': return '#4caf50';
      case 'idling': return '#2196f3';
      case 'stopped': return '#f44336';
      default: return '#757575';
    }
  };

  // Convert lat/lng to canvas coordinates
  const latLngToCanvas = (lat: number, lng: number, canvasWidth: number, canvasHeight: number) => {
    // Simple projection for Johannesburg area
    const minLat = -26.3;
    const maxLat = -25.7;
    const minLng = 27.9;
    const maxLng = 28.2;

    const x = ((lng - minLng) / (maxLng - minLng)) * canvasWidth;
    const y = ((maxLat - lat) / (maxLat - minLat)) * canvasHeight;

    return { x, y };
  };

  // Draw the map
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    const canvasWidth = rect.width;
    const canvasHeight = rect.height;

    // Clear canvas
    ctx.fillStyle = '#f0f9ff';
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);

    // Draw grid lines (streets simulation)
    ctx.strokeStyle = '#e5e7eb';
    ctx.lineWidth = 1;
    for (let i = 0; i < canvasWidth; i += 50) {
      ctx.beginPath();
      ctx.moveTo(i, 0);
      ctx.lineTo(i, canvasHeight);
      ctx.stroke();
    }
    for (let i = 0; i < canvasHeight; i += 50) {
      ctx.beginPath();
      ctx.moveTo(0, i);
      ctx.lineTo(canvasWidth, i);
      ctx.stroke();
    }

    // Draw major roads (thicker lines)
    ctx.strokeStyle = '#d1d5db';
    ctx.lineWidth = 2;
    for (let i = 0; i < canvasWidth; i += 100) {
      ctx.beginPath();
      ctx.moveTo(i, 0);
      ctx.lineTo(i, canvasHeight);
      ctx.stroke();
    }
    for (let i = 0; i < canvasHeight; i += 100) {
      ctx.beginPath();
      ctx.moveTo(0, i);
      ctx.lineTo(canvasWidth, i);
      ctx.stroke();
    }

    // Draw area labels
    ctx.fillStyle = '#6b7280';
    ctx.font = '12px Arial';
    ctx.fillText('Johannesburg CBD', 50, 350);
    ctx.fillText('Rosebank', 150, 300);
    ctx.fillText('Sandton', 250, 250);
    ctx.fillText('Midrand', 350, 200);
    ctx.fillText('Centurion', 450, 150);
    ctx.fillText('Pretoria', 500, 100);

    // Draw vehicles
    vehicles.forEach(vehicle => {
      const { x, y } = latLngToCanvas(vehicle.lat, vehicle.lng, canvasWidth, canvasHeight);
      
      // Vehicle circle
      ctx.beginPath();
      ctx.arc(x, y, hoveredVehicle === vehicle.id ? 12 : 8, 0, 2 * Math.PI);
      ctx.fillStyle = getStatusColor(vehicle.status);
      ctx.fill();
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 2;
      ctx.stroke();

      // Vehicle direction indicator (if moving)
      if (vehicle.status === 'moving' && vehicle.heading !== undefined) {
        const headingRad = (vehicle.heading * Math.PI) / 180;
        const arrowLength = 15;
        const arrowX = x + Math.cos(headingRad) * arrowLength;
        const arrowY = y + Math.sin(headingRad) * arrowLength;

        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.lineTo(arrowX, arrowY);
        ctx.strokeStyle = getStatusColor(vehicle.status);
        ctx.lineWidth = 3;
        ctx.stroke();

        // Arrow head
        const arrowHeadLength = 5;
        const arrowHeadAngle = Math.PI / 6;
        
        ctx.beginPath();
        ctx.moveTo(arrowX, arrowY);
        ctx.lineTo(
          arrowX - arrowHeadLength * Math.cos(headingRad - arrowHeadAngle),
          arrowY - arrowHeadLength * Math.sin(headingRad - arrowHeadAngle)
        );
        ctx.moveTo(arrowX, arrowY);
        ctx.lineTo(
          arrowX - arrowHeadLength * Math.cos(headingRad + arrowHeadAngle),
          arrowY - arrowHeadLength * Math.sin(headingRad + arrowHeadAngle)
        );
        ctx.stroke();
      }

      // Vehicle label
      if (hoveredVehicle === vehicle.id) {
        ctx.fillStyle = '#1f2937';
        ctx.font = 'bold 10px Arial';
        ctx.fillText(vehicle.registrationNumber, x + 15, y - 5);
        ctx.font = '9px Arial';
        ctx.fillStyle = '#6b7280';
        ctx.fillText(`${vehicle.speed} km/h`, x + 15, y + 8);
      }
    });

  }, [vehicles, hoveredVehicle]);

  // Handle mouse events
  const handleMouseMove = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const mouseX = event.clientX - rect.left;
    const mouseY = event.clientY - rect.top;

    let foundVehicle: string | null = null;

    vehicles.forEach(vehicle => {
      const { x, y } = latLngToCanvas(vehicle.lat, vehicle.lng, rect.width, rect.height);
      const distance = Math.sqrt((mouseX - x) ** 2 + (mouseY - y) ** 2);
      
      if (distance <= 12) {
        foundVehicle = vehicle.id;
      }
    });

    setHoveredVehicle(foundVehicle);
    canvas.style.cursor = foundVehicle ? 'pointer' : 'default';
  };

  const handleClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const mouseX = event.clientX - rect.left;
    const mouseY = event.clientY - rect.top;

    vehicles.forEach(vehicle => {
      const { x, y } = latLngToCanvas(vehicle.lat, vehicle.lng, rect.width, rect.height);
      const distance = Math.sqrt((mouseX - x) ** 2 + (mouseY - y) ** 2);
      
      if (distance <= 12) {
        onVehicleClick(vehicle);
      }
    });
  };

  return (
    <div className="relative w-full h-full bg-blue-50">
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        onMouseMove={handleMouseMove}
        onClick={handleClick}
      />
      
      {/* Map Legend */}
      <div className="absolute bottom-4 left-4 bg-white p-3 rounded-lg shadow-lg">
        <h4 className="text-sm font-semibold mb-2">Vehicle Status</h4>
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span className="text-xs">Moving</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <span className="text-xs">Idling</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <span className="text-xs">Stopped</span>
          </div>
        </div>
      </div>

      {/* Map Info */}
      <div className="absolute top-4 left-4 bg-white p-2 rounded-lg shadow-lg">
        <div className="text-xs text-gray-600">
          <div>Johannesburg - Pretoria Area</div>
          <div>Simulated Map View</div>
        </div>
      </div>
    </div>
  );
};

export default SimulatedMap;