import React from 'react';
import { But<PERSON> } from './button';
import { AlertTriangle } from 'lucide-react';
import type { ApiError } from '@/store/slices/errorSlice';

interface Props {
  error: ApiError;
  onDismiss: (id: string) => void;
}

export const GlobalErrorDisplay: React.FC<Props> = ({ error, onDismiss }) => {
  if (!error) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg p-6 max-w-md w-full">
        <div className="flex items-center mb-4">
          <AlertTriangle className="h-6 w-6 text-red-600 mr-3" />
          <h3 className="text-lg font-semibold">Critical Error</h3>
        </div>
        <p className="text-gray-600 mb-6">{error.message}</p>
        <div className="flex space-x-3">
          <Button
            onClick={() => window.location.reload()}
            className="flex-1"
          >
            Reload Page
          </Button>
          <Button
            onClick={() => onDismiss(error.id)}
            variant="outline"
            className="flex-1"
          >
            Dismiss
          </Button>
        </div>
      </div>
    </div>
  );
};
