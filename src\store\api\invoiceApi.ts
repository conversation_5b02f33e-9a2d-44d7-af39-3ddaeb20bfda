import { api } from '../api';
import type { PaginatedResponse } from '../../types/api';

export interface Invoice {
  id: string;
  invoice_number: string;
  work_order_id: string;
  vendor_id: string;
  vendor_name: string;
  amount: number;
  tax_amount: number;
  total_amount: number;
  status: 'pending' | 'approved' | 'paid' | 'rejected' | 'overdue';
  due_date: string;
  issued_date: string;
  paid_date?: string;
  description: string;
  line_items: InvoiceLineItem[];
  created_at: string;
  updated_at: string;
}

export interface InvoiceLineItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  total_price: number;
}

export interface InvoiceFilters {
  page?: number;
  limit?: number;
  status?: string;
  vendor_id?: string;
  date_from?: string;
  date_to?: string;
  amount_min?: number;
  amount_max?: number;
}

export interface PaymentRequest {
  invoice_id: string;
  amount: number;
  payment_method: 'bank_transfer' | 'eft' | 'cheque';
  reference_number: string;
  notes?: string;
}

export const invoiceApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get invoices with filtering
    getInvoices: builder.query<PaginatedResponse<Invoice>, InvoiceFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        
        params.append('page', String(filters.page || 1));
        params.append('limit', String(filters.limit || 20));
        
        if (filters.status) params.append('status', filters.status);
        if (filters.vendor_id) params.append('vendor_id', filters.vendor_id);
        if (filters.date_from) params.append('date_from', filters.date_from);
        if (filters.date_to) params.append('date_to', filters.date_to);
        if (filters.amount_min) params.append('amount_min', String(filters.amount_min));
        if (filters.amount_max) params.append('amount_max', String(filters.amount_max));
        
        return {
          url: '/invoices',
          params: Object.fromEntries(params),
        };
      },
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: 'Invoice' as const, id })),
              { type: 'Invoice', id: 'LIST' },
            ]
          : [{ type: 'Invoice', id: 'LIST' }],
    }),

    // Get single invoice
    getInvoice: builder.query<Invoice, string>({
      query: (id) => `/invoices/${id}`,
      providesTags: (result, error, id) => [{ type: 'Invoice', id }],
    }),

    // Approve invoice
    approveInvoice: builder.mutation<Invoice, { id: string; notes?: string }>({
      query: ({ id, notes }) => ({
        url: `/invoices/${id}/approve`,
        method: 'POST',
        body: { notes },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Invoice', id },
        { type: 'Invoice', id: 'LIST' },
      ],
    }),

    // Reject invoice
    rejectInvoice: builder.mutation<Invoice, { id: string; reason: string }>({
      query: ({ id, reason }) => ({
        url: `/invoices/${id}/reject`,
        method: 'POST',
        body: { reason },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Invoice', id },
        { type: 'Invoice', id: 'LIST' },
      ],
    }),

    // Process payment
    processPayment: builder.mutation<Invoice, PaymentRequest>({
      query: (paymentData) => ({
        url: '/invoices/payment',
        method: 'POST',
        body: paymentData,
      }),
      invalidatesTags: (result, error, { invoice_id }) => [
        { type: 'Invoice', id: invoice_id },
        { type: 'Invoice', id: 'LIST' },
      ],
    }),

    // Get invoice statistics
    getInvoiceStats: builder.query<{
      total_pending: number;
      total_approved: number;
      total_paid: number;
      total_amount_pending: number;
      total_amount_paid: number;
      overdue_count: number;
    }, { date_from?: string; date_to?: string }>({
      query: (filters = {}) => ({
        url: '/invoices/stats',
        params: filters,
      }),
      providesTags: [{ type: 'Invoice', id: 'STATS' }],
    }),
  }),
});

export const {
  useGetInvoicesQuery,
  useGetInvoiceQuery,
  useApproveInvoiceMutation,
  useRejectInvoiceMutation,
  useProcessPaymentMutation,
  useGetInvoiceStatsQuery,
} = invoiceApi;