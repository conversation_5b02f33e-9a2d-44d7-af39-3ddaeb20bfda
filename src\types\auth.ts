export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  permissions: Permission[];
  department?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export type UserRole = 
  | 'fleet_manager'
  | 'transport_officer' 
  | 'finance_officer'
  | 'driver'
  | 'auditor'
  | 'admin'
  | 'merchant'
  | 'inspector';

export type Permission = 
  | 'view_vehicles'
  | 'manage_vehicles'
  | 'view_work_orders'
  | 'manage_work_orders'
  | 'view_vendors'
  | 'manage_vendors'
  | 'view_reports'
  | 'manage_reports'
  | 'view_finances'
  | 'manage_finances'
  | 'admin_access'
  | 'manage_quotes'
  | 'submit_invoices'
  | 'view_payments'
  | 'conduct_inspections'
  | 'view_merchants'
  | 'manage_inspections';


