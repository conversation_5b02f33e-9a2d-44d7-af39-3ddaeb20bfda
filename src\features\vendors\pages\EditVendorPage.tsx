import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { ArrowLeft, Save, X, ArrowRight, FileText, MapPin, Users, DollarSign, Award } from 'lucide-react';
import { mockVendors } from '@/utils/mockData';
import { USE_MOCK_DATA } from '@/utils/mockDataConfig';
import type { VendorFormData, VendorService } from '@/types/vendor';
import { useNotifications } from '@/hooks/useNotifications';
import { useGetVendorQuery, useUpdateVendorMutation } from '@/store/api/vendorApi';

const STEPS = [
  { id: 1, title: 'Basic Information', icon: FileText },
  { id: 2, title: 'Location & Capacity', icon: MapPin },
  { id: 3, title: 'HDI Ownership', icon: Users },
  { id: 4, title: 'Historical Data', icon: DollarSign },
  { id: 5, title: 'Services & Certifications', icon: Award },
];

const EditVendorPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  
  // Use mock data instead of API
  const mockVendor = mockVendors.find(v => v.id === id);
  const vendorData = mockVendor;
  const isLoadingVendor = false;

  const [updateVendor, { isLoading: isUpdating }] = useUpdateVendorMutation();
  const { showSuccess, showError } = useNotifications();
  
  const [formData, setFormData] = useState<VendorFormData>({
    // Basic Information
    name: '',
    contactPerson: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    province: '',
    
    // Location & Capacity
    latitude: '',
    longitude: '',
    serviceRadius: 50,
    capacity: 1,
    
    // HDI Ownership
    hdiOwnership: 0,
    ownershipStructure: '',
    blackOwnership: 0,
    womenOwnership: 0,
    youthOwnership: 0,
    
    // Historical Data
    previousContracts: 0,
    totalHistoricalSpending: 0,
    averageContractValue: 0,
    
    // Existing fields
    specializations: [],
    bbbeeLevel: 1,
    certifications: [],
    services: []
  });

  const [newSpecialization, setNewSpecialization] = useState('');
  const [newCertification, setNewCertification] = useState('');
  const [newService, setNewService] = useState('');

  const progress = (currentStep / STEPS.length) * 100;

  useEffect(() => {
    if (vendorData) {
      setFormData({
        // Basic Information
        name: vendorData.name,
        contactPerson: vendorData.contactPerson,
        email: vendorData.email,
        phone: vendorData.phone,
        address: vendorData.address || '',
        city: vendorData.city,
        province: vendorData.province,
        
        // Location & Capacity
        latitude: vendorData.latitude?.toString() || '',
        longitude: vendorData.longitude?.toString() || '',
        serviceRadius: vendorData.serviceRadius || 50,
        capacity: vendorData.capacity || 1,
        
        // HDI Ownership
        hdiOwnership: vendorData.hdiOwnership || 0,
        ownershipStructure: vendorData.ownershipStructure || '',
        blackOwnership: vendorData.blackOwnership || 0,
        womenOwnership: vendorData.womenOwnership || 0,
        youthOwnership: vendorData.youthOwnership || 0,
        
        // Historical Data
        previousContracts: vendorData.previousContracts || 0,
        totalHistoricalSpending: vendorData.totalHistoricalSpending || 0,
        averageContractValue: vendorData.averageContractValue || 0,
        
        // Existing fields
        specializations: vendorData.specializations || [],
        bbbeeLevel: vendorData.bbbeeLevel || 1,
        certifications: vendorData.certifications || [],
        services: vendorData.services?.map(service => 
          typeof service === 'string' ? service : service.name
        ) || []
      });
    }
  }, [vendorData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: ['bbbeeLevel', 'serviceRadius', 'capacity', 'hdiOwnership', 'blackOwnership', 'womenOwnership', 'youthOwnership', 'previousContracts', 'totalHistoricalSpending', 'averageContractValue'].includes(name) 
        ? parseFloat(value) || 0 
        : value
    }));
  };

  const nextStep = () => {
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const addSpecialization = () => {
    if (newSpecialization.trim()) {
      setFormData(prev => ({
        ...prev,
        specializations: [...prev.specializations, newSpecialization.trim()]
      }));
      setNewSpecialization('');
    }
  };

  const removeSpecialization = (index: number) => {
    setFormData(prev => ({
      ...prev,
      specializations: prev.specializations.filter((_: string, i: number) => i !== index)
    }));
  };

  const addCertification = () => {
    if (newCertification.trim()) {
      setFormData(prev => ({
        ...prev,
        certifications: [...prev.certifications, newCertification.trim()]
      }));
      setNewCertification('');
    }
  };

  const removeCertification = (index: number) => {
    setFormData(prev => ({
      ...prev,
      certifications: prev.certifications.filter((_: string, i: number) => i !== index)
    }));
  };

  const addService = () => {
    if (newService.trim()) {
      setFormData(prev => ({
        ...prev,
        services: [...prev.services, newService.trim()]
      }));
      setNewService('');
    }
  };

  const removeService = (index: number) => {
    setFormData(prev => ({
      ...prev,
      services: prev.services.filter((_: string, i: number) => i !== index)
    }));
  };

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setFormData(prev => ({
            ...prev,
            latitude: position.coords.latitude.toString(),
            longitude: position.coords.longitude.toString()
          }));
        },
        (error) => {
          showError('Unable to get current location');
        }
      );
    } else {
      showError('Geolocation is not supported by this browser');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!id) return;

    try {
      if (USE_MOCK_DATA) {
        // Mock update
        showSuccess('Vendor updated successfully!');
        navigate(`/vendors/${id}`);
      } else {
        // Real API update
        await updateVendor({ id, data: formData }).unwrap();
        showSuccess('Vendor updated successfully!');
        navigate(`/vendors/${id}`);
      }
    } catch (error: unknown) {
      console.error('Failed to update vendor:', error);
      if (error && typeof error === 'object' && 'data' in error && 
          error.data && typeof error.data === 'object' && 'message' in error.data &&
          typeof error.data.message === 'string') {
        showError(error.data.message);
      } else {
        showError('Failed to update vendor. Please try again.');
      }
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <Card className="w-full">
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Company Name *
                  </label>
                  <Input
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Contact Person *
                  </label>
                  <Input
                    name="contactPerson"
                    value={formData.contactPerson}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email *
                  </label>
                  <Input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone *
                  </label>
                  <Input
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Address
                  </label>
                  <Input
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    City *
                  </label>
                  <Input
                    name="city"
                    value={formData.city}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Province *
                  </label>
                  <select
                    name="province"
                    value={formData.province}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-700"
                  >
                    <option value="">Select Province</option>
                    <option value="Gauteng">Gauteng</option>
                    <option value="Western Cape">Western Cape</option>
                    <option value="KwaZulu-Natal">KwaZulu-Natal</option>
                    <option value="Eastern Cape">Eastern Cape</option>
                    <option value="Free State">Free State</option>
                    <option value="Limpopo">Limpopo</option>
                    <option value="Mpumalanga">Mpumalanga</option>
                    <option value="North West">North West</option>
                    <option value="Northern Cape">Northern Cape</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    B-BBEE Level
                  </label>
                  <select
                    name="bbbeeLevel"
                    value={formData.bbbeeLevel}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-700"
                  >
                    <option value={1}>Level 1</option>
                    <option value={2}>Level 2</option>
                    <option value={3}>Level 3</option>
                    <option value={4}>Level 4</option>
                    <option value={5}>Level 5</option>
                    <option value={6}>Level 6</option>
                    <option value={7}>Level 7</option>
                    <option value={8}>Level 8</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case 2:
        return (
          <Card className="w-full">
            <CardHeader>
              <CardTitle>Location & Capacity</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Latitude
                  </label>
                  <Input
                    name="latitude"
                    value={formData.latitude}
                    onChange={handleInputChange}
                    placeholder="e.g., -26.2041"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Longitude
                  </label>
                  <Input
                    name="longitude"
                    value={formData.longitude}
                    onChange={handleInputChange}
                    placeholder="e.g., 28.0473"
                  />
                </div>
                <div className="md:col-span-2">
                  <Button
                    type="button"
                    onClick={getCurrentLocation}
                    variant="outline"
                    className="w-full"
                  >
                    <MapPin className="h-4 w-4 mr-2" />
                    Get Current Location
                  </Button>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Service Radius (km)
                  </label>
                  <Input
                    type="number"
                    name="serviceRadius"
                    value={formData.serviceRadius}
                    onChange={handleInputChange}
                    min="1"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Vehicle Capacity *
                  </label>
                  <Input
                    type="number"
                    name="capacity"
                    value={formData.capacity}
                    onChange={handleInputChange}
                    min="1"
                    required
                  />
                  <p className="text-sm text-gray-500 mt-1">Number of vehicles they can service simultaneously</p>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case 3:
        return (
          <Card className="w-full">
            <CardHeader>
              <CardTitle>HDI Ownership Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    HDI Ownership (%)
                  </label>
                  <Input
                    type="number"
                    name="hdiOwnership"
                    value={formData.hdiOwnership}
                    onChange={handleInputChange}
                    min="0"
                    max="100"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Ownership Structure
                  </label>
                  <select
                    name="ownershipStructure"
                    value={formData.ownershipStructure}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-700"
                  >
                    <option value="">Select Structure</option>
                    <option value="sole_proprietorship">Sole Proprietorship</option>
                    <option value="partnership">Partnership</option>
                    <option value="private_company">Private Company</option>
                    <option value="public_company">Public Company</option>
                    <option value="cooperative">Cooperative</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Black Ownership (%)
                  </label>
                  <Input
                    type="number"
                    name="blackOwnership"
                    value={formData.blackOwnership}
                    onChange={handleInputChange}
                    min="0"
                    max="100"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Women Ownership (%)
                  </label>
                  <Input
                    type="number"
                    name="womenOwnership"
                    value={formData.womenOwnership}
                    onChange={handleInputChange}
                    min="0"
                    max="100"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Youth Ownership (%)
                  </label>
                  <Input
                    type="number"
                    name="youthOwnership"
                    value={formData.youthOwnership}
                    onChange={handleInputChange}
                    min="0"
                    max="100"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case 4:
        return (
          <Card className="w-full">
            <CardHeader>
              <CardTitle>Historical Data & Expenditure</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Previous Contracts
                  </label>
                  <Input
                    type="number"
                    name="previousContracts"
                    value={formData.previousContracts}
                    onChange={handleInputChange}
                    min="0"
                  />
                  <p className="text-sm text-gray-500 mt-1">Number of contracts completed</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Total Historical Spending (R)
                  </label>
                  <Input
                    type="number"
                    name="totalHistoricalSpending"
                    value={formData.totalHistoricalSpending}
                    onChange={handleInputChange}
                    min="0"
                    step="0.01"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Average Contract Value (R)
                  </label>
                  <Input
                    type="number"
                    name="averageContractValue"
                    value={formData.averageContractValue}
                    onChange={handleInputChange}
                    min="0"
                    step="0.01"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case 5:
        return (
          <div className="space-y-6">
            <Card className="w-full">
              <CardHeader>
                <CardTitle>Specializations</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    placeholder="Add specialization"
                    value={newSpecialization}
                    onChange={(e) => setNewSpecialization(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSpecialization())}
                  />
                  <Button type="button" onClick={addSpecialization}>Add</Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.specializations.map((spec: string, index: number) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center gap-2"
                    >
                      {spec}
                      <button
                        type="button"
                        onClick={() => removeSpecialization(index)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </span>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="w-full">
              <CardHeader>
                <CardTitle>Certifications</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    placeholder="Add certification"
                    value={newCertification}
                    onChange={(e) => setNewCertification(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addCertification())}
                  />
                  <Button type="button" onClick={addCertification}>Add</Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.certifications.map((cert: string, index: number) => (
                    <span
                      key={index}
                      className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm flex items-center gap-2"
                    >
                      {cert}
                      <button
                        type="button"
                        onClick={() => removeCertification(index)}
                        className="text-purple-600 hover:text-purple-800"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </span>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="w-full">
              <CardHeader>
                <CardTitle>Services</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    placeholder="Add service"
                    value={newService}
                    onChange={(e) => setNewService(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addService())}
                  />
                  <Button type="button" onClick={addService}>Add</Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.services.map((service: string, index: number) => (
                    <span
                      key={index}
                      className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm flex items-center gap-2"
                    >
                      {service}
                      <button
                        type="button"
                        onClick={() => removeService(index)}
                        className="text-green-600 hover:text-green-800"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </span>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  if (isLoadingVendor) {
    return <div className="container mx-auto px-4 py-6">Loading vendor...</div>;
  }

  if (!vendorData) {
    return <div className="container mx-auto px-4 py-6">Vendor not found</div>;
  }

  return (
    <div className="container mx-auto px-6 py-8 max-w-4xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => navigate(`/vendors/${id}`)}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Vendor</span>
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Edit Vendor</h1>
            <p className="text-gray-600">Update vendor information</p>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="w-full space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">Vendor Update Progress</h2>
          <span className="text-sm text-gray-600">
            Step {currentStep} of {STEPS.length}
          </span>
        </div>
        <Progress value={progress} className="h-2 w-full" />
        
        {/* Step indicators */}
        <div className="flex justify-between">
          {STEPS.map((step) => {
            const Icon = step.icon;
            return (
              <div
                key={step.id}
                className={`flex flex-col items-center space-y-2 ${
                  step.id <= currentStep ? 'text-blue-600' : 'text-gray-400'
                }`}
              >
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    step.id <= currentStep
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-400'
                  }`}
                >
                  <Icon className="h-5 w-5" />
                </div>
                <span className="text-xs font-medium text-center">{step.title}</span>
              </div>
            );
          })}
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {renderStepContent()}

        <div className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 1}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          <div className="flex space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate(-1)}
            >
              Cancel
            </Button>
            
            {currentStep < STEPS.length ? (
              <Button
                type="button"
                onClick={nextStep}
                className="bg-blue-700 hover:bg-blue-800"
              >
                Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <Button
                type="submit"
                disabled={isUpdating}
                className="bg-blue-700 hover:bg-blue-800"
              >
                <Save className="h-4 w-4 mr-2" />
                {isUpdating ? 'Saving...' : 'Save Changes'}
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default EditVendorPage;



