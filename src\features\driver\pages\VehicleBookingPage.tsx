import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Car } from 'lucide-react';
import { useNotifications } from '@/hooks/useNotifications';
import { useAppDispatch } from '@/hooks/redux';
import { openModal } from '@/store/slices/uiSlice';

interface BookingFormData {
  vehicleType: string;
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  destination: string;
  purpose: string;
  passengers: string;
  notes: string;
}

const VehicleBookingPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { showSuccess, showError, showWarning } = useNotifications();

  const [formData, setFormData] = useState<BookingFormData>({
    vehicleType: '',
    startDate: '',
    endDate: '',
    startTime: '',
    endTime: '',
    destination: '',
    purpose: '',
    passengers: '1',
    notes: ''
  });

  const vehicleTypes = [
    { id: 'sedan', name: 'Sedan', available: 3 },
    { id: 'suv', name: 'SUV', available: 2 },
    { id: 'van', name: 'Van', available: 1 },
    { id: 'truck', name: 'Pickup Truck', available: 4 }
  ];

  const purposes = [
    'Official Meeting',
    'Document Delivery',
    'Site Visit',
    'Training',
    'Emergency',
    'Other'
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.vehicleType) {
      showError('Validation Error', 'Please select a vehicle type');
      return;
    }

    if (!formData.startDate || !formData.endDate) {
      showError('Validation Error', 'Please select start and end dates');
      return;
    }

    // Check if dates are valid
    const startDate = new Date(formData.startDate);
    const endDate = new Date(formData.endDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (startDate < today) {
      showError('Invalid Date', 'Start date cannot be in the past');
      return;
    }

    if (endDate < startDate) {
      showError('Invalid Date', 'End date cannot be before start date');
      return;
    }

    // Show confirmation modal
    dispatch(openModal({
      id: 'confirm-booking',
      type: 'confirm',
      props: {
        title: 'Confirm Vehicle Booking',
        message: `Are you sure you want to book a ${vehicleTypes.find(v => v.id === formData.vehicleType)?.name} for ${formData.purpose}?`,
        confirmText: 'Submit Booking',
        cancelText: 'Cancel',
        onConfirm: () => {
          // Handle booking submission
          console.log('Booking submitted:', formData);
          showSuccess('Booking Submitted', 'Your vehicle booking request has been submitted for approval');
          navigate('/driver/dashboard');
        }
      }
    }));
  };

  // Add validation for vehicle availability
  const handleVehicleTypeSelect = (typeId: string) => {
    const selectedType = vehicleTypes.find(type => type.id === typeId);
    if (selectedType && selectedType.available === 0) {
      showWarning('Vehicle Unavailable', `No ${selectedType.name} vehicles are currently available`);
      return;
    }
    setFormData({ ...formData, vehicleType: typeId });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <div className="flex items-center space-x-3">
          <button onClick={() => navigate(-1)} className="p-1">
            <ArrowLeft className="h-6 w-6" />
          </button>
          <h1 className="text-xl font-bold">Book Vehicle</h1>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="p-4 space-y-6">
        {/* Vehicle Type Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Select Vehicle Type
          </label>
          <div className="grid grid-cols-2 gap-3">
            {vehicleTypes.map((type) => (
              <button
                key={type.id}
                type="button"
                onClick={() => handleVehicleTypeSelect(type.id)}
                className={`p-4 rounded-lg border-2 text-left ${
                  formData.vehicleType === type.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 bg-white'
                }`}
              >
                <div className="flex items-center space-x-2 mb-1">
                  <Car className="h-5 w-5 text-gray-600" />
                  <span className="font-medium">{type.name}</span>
                </div>
                <p className="text-sm text-gray-500">{type.available} available</p>
              </button>
            ))}
          </div>
        </div>

        {/* Date & Time */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Start Date
            </label>
            <input
              type="date"
              value={formData.startDate}
              onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              End Date
            </label>
            <input
              type="date"
              value={formData.endDate}
              onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Start Time
            </label>
            <input
              type="time"
              value={formData.startTime}
              onChange={(e) => setFormData({ ...formData, startTime: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              End Time
            </label>
            <input
              type="time"
              value={formData.endTime}
              onChange={(e) => setFormData({ ...formData, endTime: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
        </div>

        {/* Destination */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Destination
          </label>
          <input
            type="text"
            value={formData.destination}
            onChange={(e) => setFormData({ ...formData, destination: e.target.value })}
            placeholder="Enter destination address"
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          />
        </div>

        {/* Purpose */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Purpose of Trip
          </label>
          <select
            value={formData.purpose}
            onChange={(e) => setFormData({ ...formData, purpose: e.target.value })}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          >
            <option value="">Select purpose</option>
            {purposes.map((purpose) => (
              <option key={purpose} value={purpose}>{purpose}</option>
            ))}
          </select>
        </div>

        {/* Passengers */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Number of Passengers
          </label>
          <select
            value={formData.passengers}
            onChange={(e) => setFormData({ ...formData, passengers: e.target.value })}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {[1, 2, 3, 4, 5, 6, 7, 8].map((num) => (
              <option key={num} value={num}>{num} passenger{num > 1 ? 's' : ''}</option>
            ))}
          </select>
        </div>

        {/* Notes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Additional Notes
          </label>
          <textarea
            value={formData.notes}
            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
            placeholder="Any special requirements or notes..."
            rows={3}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <button
            type="submit"
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Submit Booking Request
          </button>
        </div>
      </form>
    </div>
  );
};

export default VehicleBookingPage;
