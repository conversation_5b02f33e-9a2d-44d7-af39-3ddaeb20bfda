# Development Methodology & Constraints

## Current Development Mode
- **Planning Phase**: Create detailed implementation plans without making direct code changes
- Write comprehensive documentation including PRD, design specifications, and implementation plans
- Focus on architectural decisions and technical requirements in natural language
- Developers will write the actual code based on these specifications

## Error Handling Philosophy
- When debugging issues, even if you notice other errors, unless explicitly asked to fix them, **only fix the error specifically requested**
- Never try simpler implementations when running into issues
- Always work to fix existing functionality rather than simplifying anything
- Maintain the complexity required for government-grade systems

## File Operations Guidelines
- **To read files**: ALWAYS use the inbuilt Read File tool, NOT cat
- **To list directories**: ALWAYS use the inbuilt List Directory tool  
- **For codebase searching**: Use Codebase tool for indexing and search tool for searching files
- **For specific file searches**: Use Search Files tool
- **For file modifications**: Use Edit & Reapply tool

## Timestamp Requirements
- Always use a function to get current timestamp
- Current year is 2025
- Operating system: macOS 15.5
- Architecture: ARM Architecture (Apple Silicon M4)

## System Environment
- Never install dependencies globally - always use virtual environments
- Test thoroughly in development environment before any production changes
- Follow South African time zone requirements
- Consider government working hours and maintenance windows
---
alwaysApply: true
---
