---
globs: "*.py,requirements*.txt,Docker<PERSON>le,docker-compose*.yml"
---

# Python Development Practices for RT46-2026

## Python Zen Principles for Government Systems
Following the Zen of Python (PEP 20) with government-specific interpretations:

- **Beautiful is better than ugly** - Write clean, readable code that government auditors can understand
- **Explicit is better than implicit** - Make dependencies, configurations, and business rules clear
- **Simple is better than complex** - But not simpler than the government requirements demand
- **Complex is better than complicated** - Handle complex regulations with well-structured, not convoluted code
- **Flat is better than nested** - Avoid deep inheritance hierarchies; prefer composition
- **Sparse is better than dense** - Code should be readable, especially for compliance reviews
- **Readability counts** - Critical for government code reviews and knowledge transfer
- **Special cases aren't special enough to break the rules** - Maintain consistency across all modules
- **Although practicality beats purity** - Government deadlines matter, but don't compromise security
- **Errors should never pass silently** - Log everything for audit trails and debugging
- **Unless explicitly silenced** - But provide proper error handling for expected scenarios
- **In the face of ambiguity, refuse the temptation to guess** - Clarify requirements rather than assume
- **There should be one obvious way to do it** - Establish patterns and stick to them across the codebase
- **Now is better than never** - Deliver working software, but ensure it meets government standards
- **Although never is often better than right now** - Don't rush security-critical features

## Modular Design Principles

### Domain-Driven Design (DDD)
- **Bounded Contexts**: Each microservice represents a clear business domain
  - Vehicle Management: Vehicle registry, lifecycle, assignments
  - Work Order Management: Maintenance workflows, approvals, tracking
  - Vendor Management: Provider registration, performance, compliance
  - User Management: Authentication, authorization, profiles
  - Financial Management: Payments, invoicing, budgeting

### Module Structure Standards
- **Service Layer**: Business logic and domain rules
- **Repository Layer**: Data access abstractions
- **Controller Layer**: API endpoints and request handling  
- **Model Layer**: Data models and entity definitions
- **Utility Layer**: Shared functions and helpers

### Package Organization
```
src/
├── auth/                 # Authentication domain
│   ├── services/        # Business logic
│   ├── repositories/    # Data access
│   ├── models/          # Data models
│   └── controllers/     # API endpoints
├── vehicles/            # Vehicle management domain
├── work_orders/         # Work order domain
├── vendors/             # Vendor management domain
├── shared/              # Shared utilities and models
│   ├── database/        # Database configurations
│   ├── middleware/      # Common middleware
│   ├── utils/           # Utility functions
│   └── exceptions/      # Custom exceptions
└── config/              # Application configuration
```

## Separation of Concerns (SoC)

### Clear Responsibility Boundaries
- **Controllers**: Handle HTTP requests/responses, input validation, route handling
- **Services**: Implement business logic, coordinate between repositories, enforce business rules
- **Repositories**: Handle data persistence, database queries, caching operations
- **Models**: Define data structures, validation rules, relationship mappings
- **Middleware**: Cross-cutting concerns (logging, authentication, rate limiting)

### Business Logic Isolation
- **No database queries in controllers** - Delegate to services
- **No HTTP concerns in services** - Focus on business rules only
- **No business logic in repositories** - Pure data access operations
- **No external API calls in models** - Keep models focused on data representation

### Configuration Management
- **Environment-specific settings** in separate files
- **Secrets management** through secure configuration providers
- **Feature flags** for controlled rollouts
- **Database connection strings** externalized from code

### Error Handling Separation
- **Domain Exceptions**: Business rule violations (InvalidVehicleStatus, UnauthorizedAccess)
- **Infrastructure Exceptions**: Database errors, network failures, external service issues
- **Validation Exceptions**: Input validation failures, data format errors
- **System Exceptions**: Unexpected system errors, resource exhaustion

## Mandatory Practices
- **ALWAYS create a virtual environment first** - never install dependencies globally
- Use Python 3.11+ as the minimum version
- Follow PEP 8 style guidelines with Black formatter
- Use type hints throughout the codebase
- Structure code using FastAPI best practices

## Database Operations
- **CRITICAL**: Use SQLAlchemy Core, NOT Prisma ORM
- Fine-grained SQL control is required for government-level performance
- Use async database operations with asyncpg
- Implement proper connection pooling
- Use database migrations for schema changes

## Code Quality Requirements
- 80%+ test coverage minimum (target 90%)
- Use pytest for testing with async support
- Implement proper error handling and logging
- Use structured logging with contextual information
- Follow SOLID principles and clean architecture

## Dependency Injection Patterns
- **Constructor Injection**: Pass dependencies through class constructors
- **Interface Segregation**: Define specific interfaces for different concerns
- **Dependency Inversion**: Depend on abstractions, not concrete implementations
- **Service Registration**: Use FastAPI's dependency injection system
- **Testability**: Mock dependencies easily for unit testing

## Security Requirements
- Never store secrets in code - use environment variables
- Implement proper input validation with Pydantic
- Use password hashing with passlib
- Implement rate limiting and request validation
- Follow OWASP security guidelines

## Performance Considerations
- Use async/await patterns throughout
- Implement proper caching strategies with Redis
- Optimize database queries and use indexes
- Monitor performance and implement profiling
- Use connection pooling for database and external services

## Dependencies Management  
- Pin exact versions in requirements.txt
- Separate dev dependencies in requirements-dev.txt
- Regular security audits of dependencies
- Use virtual environments for all development

## Anti-Patterns to Avoid
- **God Objects**: Large classes that do too many things
- **Circular Dependencies**: Modules depending on each other
- **Tight Coupling**: Classes that know too much about each other
- **Magic Numbers**: Hard-coded values without explanation
- **Duplicate Code**: Repeated logic across multiple modules
- **Deep Nesting**: Complex nested if/else structures
- **Mixed Concerns**: Business logic mixed with infrastructure code
