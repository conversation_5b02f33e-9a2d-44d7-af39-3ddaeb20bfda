import React from 'react';
import { useAppSelector, useAppDispatch } from '@/hooks/redux';
import { removeNotification } from '@/store/slices/uiSlice';
import Toast from './Toast';

const ToastContainer: React.FC = () => {
  const notifications = useAppSelector((state) => state.ui.notifications);
  const dispatch = useAppDispatch();

  const handleClose = (id: string) => {
    dispatch(removeNotification(id));
  };

  return (
    <div className="fixed top-4 right-4 z-50 flex flex-col gap-2 min-w-[320px] max-w-[420px] px-4">
      {notifications.map((notification) => (
        <Toast
          key={notification.id}
          id={notification.id}
          type={notification.type}
          title={notification.message}
          message={notification.details}
          onClose={handleClose}
          action={notification.action}
        />
      ))}
    </div>
  );
};

export default ToastContainer;