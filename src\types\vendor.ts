export interface Vendor {
  id: string;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  province: string;
  postalCode: string;
  registrationNumber?: string;
  taxNumber?: string;
  
  // Location & Capacity
  latitude?: number;
  longitude?: number;
  serviceRadius?: number;
  capacity?: number;
  
  // HDI Ownership
  hdiOwnership?: number;
  ownershipStructure?: string;
  blackOwnership?: number;
  womenOwnership?: number;
  youthOwnership?: number;
  
  // Historical Data
  previousContracts?: number;
  totalHistoricalSpending?: number;
  averageContractValue?: number;
  
  services: string[];
  status: 'active' | 'inactive' | 'suspended';
  rating: number;
  totalJobs: number;
  completedJobs: number;
  averageCompletionTime: number;
  averageCost: number;
  certifications: string[];
  specializations: string[];
  bbbeeLevel: number;
  insuranceExpiry?: string;
  registrationDate: string;
  lastJobDate?: string;
  complianceScore: number;
  totalContracts: number;
  createdAt: string;
  updatedAt: string;
}

export interface VendorJob {
  id: string;
  title: string;
  date: string;
  status: 'completed' | 'in_progress' | 'cancelled';
  rating?: number;
  amount: number;
  on_time: boolean;
}

export interface VendorService {
  id: string;
  name: string;
  category: 'maintenance' | 'repair' | 'inspection' | 'towing' | 'parts';
  description?: string;
  hourlyRate?: number;
}

export interface VendorFormData {
  // Basic Information
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  province: string;
  postalCode?: string;
  registrationNumber?: string;
  taxNumber?: string;
  
  // Location & Capacity
  latitude?: string;
  longitude?: string;
  serviceRadius?: number;
  capacity?: number;
  
  // HDI Ownership
  hdiOwnership?: number;
  ownershipStructure?: string;
  blackOwnership?: number;
  womenOwnership?: number;
  youthOwnership?: number;
  
  // Historical Data
  previousContracts?: number;
  totalHistoricalSpending?: number;
  averageContractValue?: number;
  
  // Existing fields
  specializations: string[];
  bbbeeLevel: number;
  services: string[];
  certifications: string[];
  insuranceExpiry?: string;
}

export interface VendorFilters {
  status?: Vendor['status'];
  services?: string;
  city?: string;
  search?: string;
}




