# RT46-2026 Fleet Management System - MVP Plan (15 Days)

## Document Information
- **Version:** 1.1
- **Date:** January 2025
- **Author:** MVP Development Team
- **Status:** Active Sprint Plan
- **Duration:** 15 days
- **Team Size:** 4-5 developers

---

## 1. MVP Objectives

### 1.1 Primary Goals
- **Validate Core Concept:** Demonstrate fleet management value proposition
- **Stakeholder Buy-in:** Show tangible progress to government stakeholders
- **Technical Proof:** Validate architecture and technology choices
- **User Feedback:** Gather early feedback from target users

### 1.2 MVP Success Criteria
- ✅ Vehicle registration and viewing
- ✅ Basic maintenance work order creation
- ✅ Simple vendor assignment workflow
- ✅ Dashboard with key metrics
- ✅ User authentication and basic roles
- ✅ Deployed and accessible system

### 1.3 MVP Limitations (Acceptable for 15 days)
- ❌ No mobile app (web-only)
- ❌ No integrations with external systems
- ❌ No advanced analytics or AI features
- ❌ Basic UI/UX (functional over beautiful)
- ❌ Limited reporting capabilities
- ❌ Manual data entry (no bulk imports)

---

## 2. Team Structure (MVP Team)

### 2.1 Core MVP Team (4-5 people)
- **Tech Lead/Architect:** 1 person (full-stack, 40% backend, 40% frontend, 20% DevOps)
- **Backend Developer:** 1 person (Python/FastAPI specialist)
- **Frontend Developer:** 1 person (React.js specialist)
- **Full-Stack Developer:** 1 person (backend + database focus)
- **QA/DevOps:** 0.5 person (part-time, focus on deployment)

### 2.2 Roles & Responsibilities
**Tech Lead:**
- Architecture decisions and setup
- Database schema design
- API design and core endpoints
- Deployment and infrastructure

**Backend Developer:**
- FastAPI application setup
- Authentication and authorization
- Core business logic
- Database operations

**Frontend Developer:**
- React application setup
- Component library basics
- User interface implementation
- API integration

**Full-Stack Developer:**
- Database migrations and seeding
- Backend API endpoints
- Frontend forms and views
- Testing and debugging

---

## 3. 15-Day Sprint Breakdown

## Days 1-3: Foundation & Infrastructure

### Day 1: Environment Setup
**Morning (4 hours):**
- [ ] Project repository setup and team access
- [ ] Development environment standardization (Docker, VS Code Dev Containers)
- [ ] Basic CI/CD pipeline (**GitHub Actions** + **Cloud Deploy**)

**Afternoon (4 hours):**
- [ ] Google Cloud Platform project setup
- [ ] Basic infrastructure with **Terraform** (VPC, Subnets)
- [ ] **GKE cluster** configuration for core services
- [ ] **Cloud SQL for PostgreSQL** instance creation

**Deliverables:**
- Working development environment for all team members
- Core cloud infrastructure provisioned via Terraform
- CI/CD pipeline ready for first deployment

### Day 2: Database & Authentication Foundation
**Morning (4 hours):**
- [ ] Core database schema (vehicles, users, work_orders, vendors) deployed to Cloud SQL
- [ ] Database migrations setup (e.g., using Alembic)
- [ ] Seed data creation for testing
- [ ] **Firestore** database setup for future document use

**Afternoon (4 hours):**
- [ ] FastAPI application structure
- [ ] Deploy **Authelia** on GKE for OIDC, MFA, and RBAC
- [ ] Basic user model and service to integrate with Authelia
- [ ] Setup **Secret Manager** for database credentials

**Deliverables:**
- Database schema deployed and seeded
- Authentication service running on GKE
- FastAPI app able to connect to the database securely

### Day 3: Core API & Admin UI
**Morning (4 hours):**
- [ ] Vehicle and Vendor models and repositories in FastAPI
- [ ] Basic CRUD API endpoints for Vehicles and Vendors
- [ ] API error handling and Pydantic validation
- [ ] Health check endpoint for the service

**Afternoon (4 hours):**
- [ ] Deploy **Directus** on GKE
- [ ] Connect Directus to the Cloud SQL database
- [ ] Configure Directus to manage the `vehicles` and `vendors` tables
- [ ] Deploy FastAPI service to **Cloud Run**

**Deliverables:**
- Core Vehicle/Vendor API deployed on Cloud Run
- Functional **Directus** admin UI for data management
- API health checks passing

---

## Days 4-6: Vehicle Management Core

### Day 4: Vehicle Registry Backend
**Morning (4 hours):**
- [ ] Vehicle creation endpoint
- [ ] Vehicle listing with pagination
- [ ] Vehicle detail view endpoint
- [ ] Vehicle update endpoint

**Afternoon (4 hours):**
- [ ] Vehicle search functionality
- [ ] Vehicle status management
- [ ] Department association
- [ ] Basic validation rules

**Deliverables:**
- Complete vehicle CRUD API
- Vehicle search and filtering
- Postman/API testing collection

### Day 5: Vehicle Management Frontend
**Morning (4 hours):**
- [ ] React application setup
- [ ] Basic routing and navigation
- [ ] Vehicle list component
- [ ] Vehicle detail component

**Afternoon (4 hours):**
- [ ] Vehicle creation form
- [ ] Vehicle edit form
- [ ] Search and filter interface
- [ ] Basic responsive design

**Deliverables:**
- Vehicle management UI functional
- Forms working with validation
- Navigation between views

### Day 6: Vehicle Registry Polish
**Morning (4 hours):**
- [ ] Vehicle status badges and indicators
- [ ] Delete/deactivate functionality
- [ ] Bulk actions (basic)
- [ ] Error handling in UI

**Afternoon (4 hours):**
- [ ] Integration testing
- [ ] Bug fixes and refinements
- [ ] Performance optimization
- [ ] Code review and cleanup

**Deliverables:**
- Polished vehicle management system
- All CRUD operations working
- Basic error handling

---

## Days 7-9: Work Order System

### Day 7: Work Order Backend
**Morning (4 hours):**
- [ ] Work order creation endpoint
- [ ] Work order listing and filtering
- [ ] Work order status management
- [ ] Vehicle-work order relationships

**Afternoon (4 hours):**
- [ ] Work order assignment logic
- [ ] Priority management
- [ ] Basic approval workflow
- [ ] Work order updates

**Deliverables:**
- Work order CRUD API complete
- Status workflow implemented
- Assignment logic working

### Day 8: Work Order Frontend
**Morning (4 hours):**
- [ ] Work order list view
- [ ] Work order creation form
- [ ] Work order detail view
- [ ] Status update interface

**Afternoon (4 hours):**
- [ ] Work order assignment interface
- [ ] Priority indicators
- [ ] Filter and search functionality
- [ ] Responsive design

**Deliverables:**
- Work order UI fully functional
- Assignment workflow working
- Status management interface

### Day 9: Work Order Integration
**Morning (4 hours):**
- [ ] Vehicle-work order integration
- [ ] Work order history view
- [ ] Vehicle status updates from work orders
- [ ] Validation and business rules

**Afternoon (4 hours):**
- [ ] End-to-end testing
- [ ] Performance optimization
- [ ] Bug fixes
- [ ] Code cleanup

**Deliverables:**
- Complete work order system
- Integration with vehicles working
- Business logic implemented

---

## Days 10-12: Vendor Management & Assignment

### Day 10: Vendor Management Backend
**Morning (4 hours):**
- [ ] Vendor registration endpoint
- [ ] Vendor profile management
- [ ] Vendor listing and search
- [ ] Basic vendor verification

**Afternoon (4 hours):**
- [ ] Work order-vendor assignment
- [ ] Vendor capacity tracking
- [ ] Simple vendor scoring
- [ ] Vendor work history

**Deliverables:**
- Vendor CRUD API complete
- Assignment logic working
- Basic vendor analytics

### Day 11: Vendor Management Frontend
**Morning (4 hours):**
- [ ] Vendor registration form
- [ ] Vendor list and search
- [ ] Vendor profile view
- [ ] Vendor assignment interface

**Afternoon (4 hours):**
- [ ] Work order assignment to vendors
- [ ] Vendor performance indicators
- [ ] Vendor work history view
- [ ] Assignment workflow UI

**Deliverables:**
- Vendor management UI complete
- Assignment workflow functional
- Vendor profiles working

### Day 12: Vendor-Work Order Integration
**Morning (4 hours):**
- [ ] Complete assignment workflow
- [ ] Vendor notification system (basic)
- [ ] Work order status updates
- [ ] Vendor dashboard basics

**Afternoon (4 hours):**
- [ ] Integration testing
- [ ] Workflow optimization
- [ ] Bug fixes
- [ ] Performance tuning

**Deliverables:**
- End-to-end vendor assignment
- Workflow integration complete
- Basic vendor experience

---

## Days 13-15: Dashboard, Testing & Deployment

### Day 13: Dashboard & Reporting
**Morning (4 hours):**
- [ ] Main dashboard layout
- [ ] Vehicle summary widgets
- [ ] Work order summary widgets
- [ ] Key performance indicators

**Afternoon (4 hours):**
- [ ] Real-time data updates
- [ ] Basic charts and visualizations
- [ ] Department-level filtering
- [ ] Export functionality (basic)

**Deliverables:**
- Functional dashboard
- Key metrics visible
- Basic reporting capability

### Day 14: Testing & Quality Assurance
**Morning (4 hours):**
- [ ] Unit test coverage review
- [ ] Integration test execution
- [ ] End-to-end user journeys
- [ ] Performance testing

**Afternoon (4 hours):**
- [ ] Security audit (basic)
- [ ] Data validation testing
- [ ] Error handling verification
- [ ] Browser compatibility testing

**Deliverables:**
- Test coverage at 70%+
- Major bugs fixed
- Security basics verified

### Day 15: Deployment & Demo Preparation
**Morning (4 hours):**
- [ ] Production deployment
- [ ] Production database setup
- [ ] SSL/security configuration
- [ ] Monitoring setup (basic)

**Afternoon (4 hours):**
- [ ] Demo data creation
- [ ] User training materials (basic)
- [ ] Demo script preparation
- [ ] Final bug fixes

**Deliverables:**
- System deployed and accessible
- Demo environment ready
- MVP fully functional

---

## 4. Technical Architecture (MVP)

### 4.1 Simplified Technology Stack
```python
# Backend Stack
- Python 3.11+ with FastAPI on Cloud Run
- Authelia on GKE for Authentication (OIDC, MFA)
- SQLAlchemy Core 2.0+ for database interaction
- Pydantic for data validation

# Frontend & Admin UI
- React 18 with TypeScript for the main web portal
- Directus on GKE for the admin UI

# Database & Storage
- Google Cloud SQL for PostgreSQL 15
- Firestore (for future document/config use)
- Google Cloud Storage (for future file uploads)

# Infrastructure & DevOps
- Google Kubernetes Engine (GKE) for stateful services (Authelia, Directus)
- Google Cloud Run for stateless microservices
- Apigee (basic routing configured)
- Terraform for Infrastructure as Code
- GitHub Actions & Cloud Deploy for CI/CD
- Secret Manager for credentials
```

### 4.2 MVP Database Schema (Core Tables Only)
```sql
-- Essential tables for MVP
CREATE TABLE departments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    role VARCHAR(50) NOT NULL,
    department_id UUID REFERENCES departments(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE vehicles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vin VARCHAR(17) UNIQUE NOT NULL,
    registration_number VARCHAR(20) UNIQUE NOT NULL,
    make VARCHAR(50) NOT NULL,
    model VARCHAR(100) NOT NULL,
    year INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    department_id UUID REFERENCES departments(id),
    current_mileage INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE vendors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_name VARCHAR(200) NOT NULL,
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    specializations TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE work_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vehicle_id UUID REFERENCES vehicles(id),
    vendor_id UUID REFERENCES vendors(id),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    priority VARCHAR(20) DEFAULT 'medium',
    status VARCHAR(30) DEFAULT 'created',
    estimated_cost DECIMAL(10,2),
    requested_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4.3 MVP API Endpoints (Core Only)
```python
# Authentication
POST /api/auth/login
POST /api/auth/logout
GET  /api/auth/me

# Vehicles
GET    /api/vehicles          # List with pagination and search
POST   /api/vehicles          # Create new vehicle
GET    /api/vehicles/{id}     # Get vehicle details
PUT    /api/vehicles/{id}     # Update vehicle
DELETE /api/vehicles/{id}     # Deactivate vehicle

# Work Orders
GET    /api/work-orders       # List with filtering
POST   /api/work-orders       # Create work order
GET    /api/work-orders/{id}  # Get work order details
PUT    /api/work-orders/{id}  # Update work order
POST   /api/work-orders/{id}/assign  # Assign to vendor

# Vendors
GET    /api/vendors           # List vendors
POST   /api/vendors           # Register vendor
GET    /api/vendors/{id}      # Get vendor details
PUT    /api/vendors/{id}      # Update vendor

# Dashboard
GET    /api/dashboard/summary # Key metrics and KPIs
```

---

## 5. MVP Features & User Stories

### 5.1 Core User Stories (Must Have)
**As a Fleet Manager, I want to:**
1. Register new vehicles in the system
2. View all vehicles in my department
3. Create maintenance work orders for vehicles
4. Assign work orders to vendors
5. Track work order status and progress
6. View dashboard with key fleet metrics

**As a Transport Officer, I want to:**
1. View vehicles assigned to me
2. Create work orders for maintenance issues
3. Update vehicle information
4. Track maintenance history

**As a Vendor, I want to:**
1. View work orders assigned to me
2. Update work order status
3. View my work history
4. Update my company profile

### 5.2 MVP UI Wireframes (Key Screens)

#### 5.2.1 Dashboard
```
┌─────────────────────────────────────────────────────┐
│ Fleet Management Dashboard                          │
├─────────────────────────────────────────────────────┤
│ [Total Vehicles: 150] [Active: 140] [Maintenance: 10] │
│ [Open Work Orders: 25] [Pending Assignment: 8]     │
├─────────────────────────────────────────────────────┤
│ Recent Activity                                     │
│ • Vehicle ABC123 - Service completed               │
│ • Work Order #001 - Assigned to Vendor XYZ         │
│ • Vehicle DEF456 - Entered maintenance              │
└─────────────────────────────────────────────────────┘
```

#### 5.2.2 Vehicle List
```
┌─────────────────────────────────────────────────────┐
│ Vehicles [+ Add New]    [Search: _______] [Filter] │
├─────────────────────────────────────────────────────┤
│ Registration | Make/Model    | Status    | Actions  │
│ ABC123GP    | Toyota Hilux  | Active    | [View][Edit] │
│ DEF456GP    | Ford Ranger   | Maintenance| [View][Edit] │
│ GHI789GP    | Isuzu KB      | Active    | [View][Edit] │
├─────────────────────────────────────────────────────┤
│ Page 1 of 8                           [Prev][Next] │
└─────────────────────────────────────────────────────┘
```

#### 5.2.3 Work Order Creation
```
┌─────────────────────────────────────────────────────┐
│ Create Work Order                                   │
├─────────────────────────────────────────────────────┤
│ Vehicle: [Dropdown: ABC123GP - Toyota Hilux]       │
│ Title: [_________________________]                 │
│ Description: [_________________________]            │
│            [_________________________]            │
│ Priority: [Medium ▼]                               │
│ Estimated Cost: [R ___________]                    │
│                                                     │
│ [Cancel] [Create Work Order]                       │
└─────────────────────────────────────────────────────┘
```

---

## 6. Development Workflow (MVP)

### 6.1 Daily Standups (15 minutes)
- **Time:** 9:00 AM daily
- **Format:** What did you complete yesterday? What will you do today? Any blockers?
- **Focus:** Keep momentum high, address blockers immediately

### 6.2 Code Review Process
- **Rule:** All code must be reviewed before merge
- **Timeline:** Reviews completed within 2 hours
- **Focus:** Functionality over perfection (MVP mindset)

### 6.3 Testing Strategy (MVP)
- **Unit Tests:** Focus on business logic and API endpoints
- **Integration Tests:** End-to-end user workflows
- **Manual Testing:** Daily smoke tests of core features
- **Target Coverage:** 70% (pragmatic for MVP)

### 6.4 Deployment Strategy
- **Environment:** Single staging environment that becomes production
- **Deployment:** Daily deployments to maintain momentum
- **Rollback:** Simple Docker rollback strategy
- **Monitoring:** Basic health checks and error logging

---

## 7. Risk Management (MVP)

### 7.1 Critical Risks & Mitigation
**Risk: Feature Creep**
- *Mitigation:* Strict scope enforcement, "parking lot" for future features

**Risk: Technical Complexity**
- *Mitigation:* Simple solutions, avoid over-engineering

**Risk: Team Blockers**
- *Mitigation:* Daily standups, pair programming for complex issues

**Risk: Infrastructure Issues**
- *Mitigation:* Use managed services (Cloud SQL, Cloud Run)

### 7.2 Success Metrics (MVP)
- **Velocity:** All 15-day milestones met
- **Quality:** Core workflows working without critical bugs
- **Performance:** Pages load in under 3 seconds
- **Usability:** Core tasks completable without training

---

## 8. MVP Demo Script (Day 15)

### 8.1 Demo Flow (15 minutes)
1. **Login & Dashboard** (2 minutes)
   - Show user authentication
   - Display key metrics and summary

2. **Vehicle Management** (4 minutes)
   - Add new vehicle
   - Search and filter vehicles
   - View vehicle details

3. **Work Order Workflow** (6 minutes)
   - Create work order for vehicle
   - Assign work order to vendor
   - Update work order status
   - View work order history

4. **Vendor Management** (2 minutes)
   - Register new vendor
   - View vendor assignments

5. **Dashboard Metrics** (1 minute)
   - Show updated metrics
   - Demonstrate real-time updates

### 8.2 Demo Data Preparation
- 50 sample vehicles across 3 departments
- 20 work orders in various states
- 10 registered vendors
- 5 users with different roles

---

## 9. Post-MVP Planning

### 9.1 MVP Feedback Collection
- **Stakeholder Demo:** Collect formal feedback
- **User Testing:** 3-5 target users test the system
- **Technical Review:** Identify technical debt and improvements

### 9.2 MVP to Full System Roadmap
- **Phase 2 (Weeks 3-4):** Mobile app and advanced features
- **Phase 3 (Weeks 5-8):** Integrations and analytics
- **Phase 4 (Weeks 9-12):** AI features and optimization

### 9.3 MVP Success Criteria
✅ **Functional Success:** All core workflows working
✅ **Technical Success:** System deployed and accessible
✅ **Business Success:** Stakeholder approval to continue
✅ **User Success:** Positive feedback from target users

---

## 10. Resource Requirements

### 10.1 Development Tools & Services
- Google Cloud Platform account
- GitHub repository with Actions
- Development laptops/workstations
- Design tools (Figma for wireframes)
- Testing tools (Postman, browser dev tools)

### 10.2 Estimated Costs (15 days)
- **GCP Services:** ~$200 (Cloud SQL, Cloud Run, Storage)
- **Development Tools:** ~$100 (any paid subscriptions)
- **Total:** <$500 for MVP infrastructure

### 10.3 Team Availability
- **100% dedicated team:** All 4-5 team members full-time
- **Minimal meetings:** Focus on development over process
- **Clear priorities:** Everyone knows what success looks like

---

## 11. Conclusion

This 15-day MVP plan is designed to deliver a **demonstrable, functional fleet management system** that validates the core value proposition. The plan prioritizes:

1. **Speed over perfection:** Get to working software quickly
2. **Core value demonstration:** Show the most important features
3. **Stakeholder confidence:** Prove the team can deliver
4. **Technical validation:** Confirm architecture and technology choices

### 11.1 Day 15 Success Vision
By Day 15, stakeholders will see a working system where they can:
- Register and manage vehicles
- Create and assign maintenance work orders
- Track work progress and vendor performance
- View key fleet metrics on a dashboard

### 11.2 Next Steps After MVP
1. **Immediate:** Stakeholder demo and feedback collection
2. **Week 3:** Begin Phase 2 development based on feedback
3. **Ongoing:** Iterate rapidly based on user feedback

---

*Team lets work with the our daily motto of "Make it work first, make it right and make it fast"*