import { useAppDispatch } from './redux';
import { addNotification } from '@/store/slices/uiSlice';

export const useNotifications = () => {
  const dispatch = useAppDispatch();

  const showNotification = (
    message: string,
    type: 'success' | 'error' | 'warning' | 'info' = 'info',
    details?: string,
    duration?: number,
    action?: { label: string; onClick: () => void }
  ) => {
    dispatch(addNotification({
      message,
      type,
      details,
      duration,
      action
    }));
  };

  const showSuccess = (message: string, details?: string) => {
    showNotification(message, 'success', details);
  };

  const showError = (message: string, details?: string) => {
    showNotification(message, 'error', details, 8000); // Longer duration for errors
  };

  const showWarning = (message: string, details?: string) => {
    showNotification(message, 'warning', details);
  };

  const showInfo = (message: string, details?: string) => {
    showNotification(message, 'info', details);
  };

  // Predefined system notifications
  const notifications = {
    vehicleDueForService: (vehicleReg: string) => 
      showWarning('Vehicle Due for Service', `${vehicleReg} requires scheduled maintenance`),
    
    repairCompleted: (vehicleReg: string, vendor: string) => 
      showSuccess('Repair Completed', `${vehicleReg} service completed by ${vendor}`),
    
    slaBreachWarning: (workOrderId: string) => 
      showError('SLA Breach Alert', `Work order ${workOrderId} has exceeded the agreed timeline`),
    
    newTrafficFine: (vehicleReg: string, amount: number) => 
      showWarning('New Traffic Fine', `${vehicleReg} received a fine of R${amount}`),
    
    quotationReceived: (vendor: string, amount: number) => 
      showInfo('Quotation Received', `New quote from ${vendor} - R${amount.toLocaleString()}`),
    
    invoiceOverdue: (vendor: string, amount: number) => 
      showError('Invoice Overdue', `Payment to ${vendor} (R${amount.toLocaleString()}) is overdue`),
    
    driverBehaviorAlert: (driverName: string, issue: string) => 
      showWarning('Driver Behavior Alert', `${driverName}: ${issue}`),
    
    bookingApproved: (vehicleReg: string, date: string) => 
      showSuccess('Booking Approved', `Vehicle ${vehicleReg} booking confirmed for ${date}`),
    
    bookingRejected: (vehicleReg: string, reason: string) => 
      showError('Booking Rejected', `Vehicle ${vehicleReg} booking rejected: ${reason}`),
    
    paymentConfirmed: (vendor: string, amount: number) => 
      showSuccess('Payment Confirmed', `Payment of R${amount.toLocaleString()} sent to ${vendor}`),
    
    systemMaintenance: (startTime: string, duration: string) => 
      showInfo('System Maintenance', `Scheduled maintenance from ${startTime} (${duration})`),
    
    newUserAdded: (userName: string, role: string) => 
      showInfo('New User Added', `${userName} has been added as ${role}`),
    
    inspectionScheduled: (vehicleReg: string, date: string) => 
      showInfo('Inspection Scheduled', `${vehicleReg} inspection scheduled for ${date}`),
    
    roleChanged: (userName: string, newRole: string) => 
      showInfo('Role Updated', `${userName} role changed to ${newRole}`)
  };

  return {
    showNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    notifications
  };
};