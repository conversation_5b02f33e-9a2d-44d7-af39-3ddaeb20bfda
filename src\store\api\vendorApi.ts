import { api } from '../api';
import type { PaginatedResponse } from '../../types/api';
import type { Vendor, VendorJob } from '@/types/vendor';

export interface VendorApiResponse {
  id: string;
  name: string;
  registration_number: string;
  contact_person: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  province: string;
  postal_code: string;
  tax_number?: string;
  bbbee_level?: number;
  specializations: string[];
  services: string[];
  rating: number;
  total_jobs: number;
  completed_jobs: number;
  average_cost: number;
  average_completion_time: number;
  status: 'active' | 'inactive' | 'suspended';
  compliance_score: number;
  certifications: string[];
  insurance_expiry?: string;
  created_at: string;
  updated_at: string;
}

export interface VendorService {
  id: string;
  name: string;
  category: 'maintenance' | 'repair' | 'inspection' | 'emergency';
  hourly_rate?: number;
  fixed_rate?: number;
}

export interface VendorFilters {
  page?: number;
  limit?: number;
  search?: string;
  specialization?: string;
  status?: string;
  city?: string;
  province?: string;
  min_rating?: number;
}

export interface CreateVendorRequest {
  name: string;
  registration_number?: string;
  contact_person: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  province: string;
  postal_code?: string;
  tax_number?: string;
  
  // Location & Capacity
  latitude?: string;
  longitude?: string;
  serviceRadius?: number;
  capacity?: number;
  
  // HDI Ownership
  hdiOwnership?: number;
  ownershipStructure?: string;
  blackOwnership?: number;
  womenOwnership?: number;
  youthOwnership?: number;
  
  // Historical Data
  previousContracts?: number;
  totalHistoricalSpending?: number;
  averageContractValue?: number;
  
  bbbee_level?: number;
  specializations: string[];
  services: string[];
  certifications?: string[];
  insurance_expiry?: string;
}

export const vendorApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get vendors with filtering
    getVendors: builder.query<PaginatedResponse<Vendor>, VendorFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        
        params.append('page', String(filters.page || 1));
        params.append('limit', String(filters.limit || 20));
        
        if (filters.search) params.append('search', filters.search);
        if (filters.specialization) params.append('specialization', filters.specialization);
        if (filters.status) params.append('status', filters.status);
        if (filters.city) params.append('city', filters.city);
        if (filters.province) params.append('province', filters.province);
        if (filters.min_rating) params.append('min_rating', String(filters.min_rating));
        
        return {
          url: '/vendors',
          params: Object.fromEntries(params),
        };
      },
      transformResponse: (response: PaginatedResponse<VendorApiResponse>): PaginatedResponse<Vendor> => ({
        ...response,
        data: response.data.map(vendor => ({
          id: vendor.id,
          name: vendor.name,
          contactPerson: vendor.contact_person,
          email: vendor.email,
          phone: vendor.phone,
          address: vendor.address,
          city: vendor.city,
          province: vendor.province,
          postalCode: vendor.postal_code,
          taxNumber: vendor.tax_number,
          bbbeeLevel: vendor.bbbee_level || 0,
          specializations: vendor.specializations,
          services: vendor.services,
          certifications: vendor.certifications || [],
          insuranceExpiry: vendor.insurance_expiry,
          status: 'active',
          rating: 0,
          totalJobs: 0,
          completedJobs: 0,
          averageCompletionTime: 0,
          averageCost: 0,
          complianceScore: 0,
          totalContracts: 0,
          registrationDate: new Date().toISOString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }))
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: 'Vendor' as const, id })),
              { type: 'Vendor', id: 'LIST' },
            ]
          : [{ type: 'Vendor', id: 'LIST' }],
    }),

    // Get single vendor
    getVendor: builder.query<Vendor, string>({
      query: (id) => `/vendors/${id}`,
      providesTags: (_result, _error, id) => [{ type: 'Vendor', id }],
    }),

    // Create vendor
    createVendor: builder.mutation<Vendor, CreateVendorRequest>({
      query: (vendorData) => ({
        url: '/vendors',
        method: 'POST',
        body: vendorData,
      }),
      invalidatesTags: [{ type: 'Vendor', id: 'LIST' }],
    }),

    // Update vendor
    updateVendor: builder.mutation<Vendor, { id: string; data: Partial<CreateVendorRequest> }>({
      query: ({ id, data }) => ({
        url: `/vendors/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: 'Vendor', id },
        { type: 'Vendor', id: 'LIST' },
      ],
    }),

    // Get vendor performance
    getVendorPerformance: builder.query<{
      completion_rate: number;
      average_rating: number;
      on_time_delivery: number;
      cost_efficiency: number;
      recent_jobs: VendorJob[];
    }, string>({
      query: (id) => `/vendors/${id}/performance`,
      providesTags: (_result, _error, id) => [{ type: 'Vendor', id: `${id}_PERFORMANCE` }],
    }),

    // Get vendor specializations
    getVendorSpecializations: builder.query<string[], void>({
      query: () => '/vendors/specializations',
    }),

    // Delete vendor
    deleteVendor: builder.mutation<{ success: boolean; message: string }, string>({
      query: (id) => ({
        url: `/vendors/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Vendor', id },
        { type: 'Vendor', id: 'LIST' },
      ],
    }),
  }),
});

export const {
  useGetVendorsQuery,
  useGetVendorQuery,
  useCreateVendorMutation,
  useUpdateVendorMutation,
  useDeleteVendorMutation,
  useGetVendorPerformanceQuery,
  useGetVendorSpecializationsQuery,
} = vendorApi;











