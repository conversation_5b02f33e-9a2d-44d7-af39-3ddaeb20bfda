import React, { useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  ArrowLeft, 
  Edit, 
  Star, 
  MapPin,
  Users, 
  CheckCircle,
  AlertTriangle,
  Award,
  Trash2,
  DollarSign
} from 'lucide-react';
import { useGetVendorQuery, useDeleteVendorMutation } from '@/store/api/vendorApi';
import { useNotifications } from '@/hooks/useNotifications';
import DeleteConfirmModal from '@/components/modals/DeleteConfirmModal';
import { mockVendors } from '@/utils/mockData';

const VendorDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotifications();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  
  // Use mock data instead of API
  const mockVendor = mockVendors.find(v => v.id === id);
  const vendorData = mockVendor;
  const isLoading = false;
  const error = null;

  // Remove or comment out the API call
  // const {
  //   data: vendor,
  //   isLoading,
  //   error
  // } = useGetVendorQuery(id!, { 
  //   skip: !id || mockVendor
  // });

  const [deleteVendor, { isLoading: isDeleting }] = useDeleteVendorMutation();

  // Use mock data if enabled
  // const mockVendor = USE_MOCK_DATA ? mockVendors.find(v => v.id === id) : null;
  // const vendorData = USE_MOCK_DATA ? mockVendor : vendor;

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!id) return;

    try {
      if (mockVendor) {
        // Mock deletion
        setShowDeleteModal(false);
        showSuccess('Vendor deleted successfully');
        navigate('/vendors');
      } else {
        // Real API deletion
        await deleteVendor(id).unwrap();
        setShowDeleteModal(false);
        showSuccess('Vendor deleted successfully');
        navigate('/vendors');
      }
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'data' in error && 
          error.data && typeof error.data === 'object' && 'message' in error.data &&
          typeof error.data.message === 'string') {
        showError(error.data.message);
      } else {
        showError('Failed to delete vendor. Please try again.');
      }
    }
  };

  if (isLoading && !mockVendor) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading vendor details...</div>
      </div>
    );
  }

  if (!vendorData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-red-500">Vendor not found</div>
      </div>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'inactive':
        return <AlertTriangle className="h-5 w-5 text-gray-500" />;
      case 'suspended':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  return (
    <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8 space-y-4 sm:space-y-6 max-w-7xl">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
          <Button
            variant="outline"
            onClick={() => navigate('/vendors')}
            className="flex items-center space-x-2 self-start"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="hidden sm:inline">Back to Vendors</span>
            <span className="sm:hidden">Back</span>
          </Button>
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">{vendorData.name}</h1>
            <p className="text-sm sm:text-base text-gray-600">Vendor Details</p>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
          <Button onClick={() => navigate(`/vendors/${id}/edit`)} className="h-10 sm:h-auto text-sm sm:text-base">
            <Edit className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Edit Vendor</span>
            <span className="sm:hidden">Edit</span>
          </Button>
          <Button
            variant="destructive"
            onClick={handleDeleteClick}
            className="h-10 sm:h-auto text-sm sm:text-base"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Delete Vendor</span>
            <span className="sm:hidden">Delete</span>
          </Button>
        </div>
      </div>

      {/* Vendor Details Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {/* Basic Information Card */}
        <Card className="w-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm text-gray-500">Contact Person</p>
              <p className="font-medium">{vendorData.contactPerson}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Email</p>
              <p className="font-medium">{vendorData.email}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Phone</p>
              <p className="font-medium">{vendorData.phone}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Address</p>
              <p className="font-medium">{vendorData.address || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Location</p>
              <p className="font-medium">{vendorData.city}, {vendorData.province}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">B-BBEE Level</p>
              <p className="font-medium">Level {vendorData.bbbeeLevel || 'N/A'}</p>
            </div>
          </CardContent>
        </Card>

        {/* Location & Capacity Card */}
        <Card className="w-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Location & Capacity
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm text-gray-500">Coordinates</p>
              <p className="font-medium">
                {vendorData.latitude && vendorData.longitude 
                  ? `${vendorData.latitude}, ${vendorData.longitude}`
                  : 'N/A'
                }
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Service Radius</p>
              <p className="font-medium">{vendorData.serviceRadius || 'N/A'} km</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Vehicle Capacity</p>
              <p className="font-medium">{vendorData.capacity || 'N/A'} vehicles</p>
            </div>
          </CardContent>
        </Card>

        {/* HDI Ownership Card */}
        <Card className="w-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              HDI Ownership
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm text-gray-500">HDI Ownership</p>
              <p className="font-medium">{vendorData.hdiOwnership || 0}%</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Ownership Structure</p>
              <p className="font-medium">{vendorData.ownershipStructure || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Black Ownership</p>
              <p className="font-medium">{vendorData.blackOwnership || 0}%</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Women Ownership</p>
              <p className="font-medium">{vendorData.womenOwnership || 0}%</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Youth Ownership</p>
              <p className="font-medium">{vendorData.youthOwnership || 0}%</p>
            </div>
          </CardContent>
        </Card>

        {/* Historical Data Card */}
        <Card className="w-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Historical Data
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm text-gray-500">Previous Contracts</p>
              <p className="font-medium">{vendorData.previousContracts || 0}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Total Historical Spending</p>
              <p className="font-medium">R{vendorData.totalHistoricalSpending?.toLocaleString() || '0'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Average Contract Value</p>
              <p className="font-medium">R{vendorData.averageContractValue?.toLocaleString() || '0'}</p>
            </div>
          </CardContent>
        </Card>

        {/* Performance Metrics Card */}
        <Card className="w-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm text-gray-500">Rating</p>
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="font-medium">{vendorData.rating || 'N/A'}</span>
              </div>
            </div>
            <div>
              <p className="text-sm text-gray-500">Total Jobs</p>
              <p className="font-medium">{vendorData.totalJobs || 0}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Average Cost</p>
              <p className="font-medium">R{vendorData.averageCost || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Completion Time</p>
              <p className="font-medium">{vendorData.averageCompletionTime || 'N/A'}</p>
            </div>
          </CardContent>
        </Card>

        {/* Services & Certifications Card */}
        <Card className="w-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Services & Certifications
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm text-gray-500 mb-2">Services</p>
              <div className="flex flex-wrap gap-1">
                {vendorData.services?.map((service, index) => (
                  <span
                    key={index}
                    className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs"
                  >
                    {service}
                  </span>
                ))}
              </div>
            </div>
            <div>
              <p className="text-sm text-gray-500 mb-2">Specializations</p>
              <div className="flex flex-wrap gap-1">
                {vendorData.specializations?.map((spec, index) => (
                  <span
                    key={index}
                    className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs"
                  >
                    {spec}
                  </span>
                ))}
              </div>
            </div>
            <div>
              <p className="text-sm text-gray-500 mb-2">Certifications</p>
              <div className="flex flex-wrap gap-1">
                {vendorData.certifications?.map((cert, index) => (
                  <span
                    key={index}
                    className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs"
                  >
                    {cert}
                  </span>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteConfirm}
        itemName={vendorData.name}
        itemType="vendor"
        isLoading={isDeleting}
      />
    </div>
  );
};

export default VendorDetailPage;





