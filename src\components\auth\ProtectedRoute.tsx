import React, { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { Box, CircularProgress } from '@mui/material';
import { useAppSelector, useAppDispatch } from '@/hooks/redux';
import { loginSuccess } from '@/store/slices/authSlice';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: string[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredRoles 
}) => {
  const dispatch = useAppDispatch();
  const { isAuthenticated, user, token, isLoading } = useAppSelector(
    (state) => state.auth
  );

  useEffect(() => {
    // Check if we have a token but no user (page refresh scenario)
    if (token && !user && !isLoading) {
      // In a real app, you'd validate the token with the server
      // For now, we'll simulate a user based on the token
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Fleet',
        lastName: 'Manager',
        role: 'fleet_manager' as const,
        permissions: ['read', 'write', 'delete'],
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      dispatch(loginSuccess({ user: mockUser, token }));
    }
  }, [token, user, isLoading, dispatch]);

  if (isLoading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <CircularProgress />
      </Box>
    );
  }

  if (!isAuthenticated || !user) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRoles && !requiredRoles.includes(user.role)) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;