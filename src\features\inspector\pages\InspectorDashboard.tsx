import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAppDispatch } from '@/hooks/redux';
import { addNotification, openModal } from '@/store/slices/uiSlice';
import { 
  ClipboardCheck,
  Calendar,
  MapPin,
  Clock,
  CheckCircle,
  AlertTriangle,
  Search,
  Filter,
  Car,
  Building,
  User,
  Phone,
  Navigation,
  FileText,
  Camera
} from 'lucide-react';

interface Inspection {
  id: string;
  type: 'vehicle' | 'merchant';
  scheduledDate: string;
  scheduledTime: string;
  status: 'Scheduled' | 'In Progress' | 'Completed' | 'Overdue' | 'Cancelled';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  location: {
    address: string;
    coordinates?: { lat: number; lng: number };
  };
  subject: {
    id: string;
    name: string;
    registration?: string; // For vehicles
    contactPerson?: string; // For merchants
    phone: string;
  };
  inspectionReason: string;
  estimatedDuration: number; // in minutes
  notes?: string;
  completedDate?: string;
  reportId?: string;
}

const InspectorDashboard: React.FC = () => {
  const dispatch = useAppDispatch();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('today');

  // Mock data - replace with API call
  const inspections: Inspection[] = [
    {
      id: '1',
      type: 'vehicle',
      scheduledDate: '2025-01-30',
      scheduledTime: '09:00',
      status: 'Scheduled',
      priority: 'High',
      location: {
        address: 'Department of Health, Pretoria',
        coordinates: { lat: -25.7479, lng: 28.2293 }
      },
      subject: {
        id: 'V001',
        name: 'Toyota Hilux',
        registration: 'GP123ABC',
        phone: '************'
      },
      inspectionReason: 'Annual Safety Inspection',
      estimatedDuration: 45,
      notes: 'Focus on brake system and tire condition'
    },
    {
      id: '2',
      type: 'merchant',
      scheduledDate: '2025-01-30',
      scheduledTime: '11:30',
      status: 'Scheduled',
      priority: 'Medium',
      location: {
        address: 'AutoFix Workshop, Johannesburg',
        coordinates: { lat: -26.2041, lng: 28.0473 }
      },
      subject: {
        id: 'M001',
        name: 'AutoFix Workshop',
        contactPerson: 'John Smith',
        phone: '************'
      },
      inspectionReason: 'Quarterly Facility Inspection',
      estimatedDuration: 90,
      notes: 'Check compliance with safety standards'
    },
    {
      id: '3',
      type: 'vehicle',
      scheduledDate: '2025-01-30',
      scheduledTime: '14:00',
      status: 'In Progress',
      priority: 'Medium',
      location: {
        address: 'Department of Education, Cape Town',
        coordinates: { lat: -33.9249, lng: 18.4241 }
      },
      subject: {
        id: 'V002',
        name: 'Ford Ranger',
        registration: 'CA456DEF',
        phone: '************'
      },
      inspectionReason: 'Post-Accident Inspection',
      estimatedDuration: 60,
      notes: 'Assess damage and roadworthiness'
    },
    {
      id: '4',
      type: 'vehicle',
      scheduledDate: '2025-01-29',
      scheduledTime: '10:00',
      status: 'Completed',
      priority: 'Low',
      location: {
        address: 'Department of Transport, Durban'
      },
      subject: {
        id: 'V003',
        name: 'Isuzu KB',
        registration: 'KZN789GHI',
        phone: '************'
      },
      inspectionReason: 'Routine Maintenance Check',
      estimatedDuration: 30,
      completedDate: '2025-01-29T11:15:00Z',
      reportId: 'RPT-2025-001'
    },
    {
      id: '5',
      type: 'merchant',
      scheduledDate: '2025-01-28',
      scheduledTime: '15:30',
      status: 'Overdue',
      priority: 'Critical',
      location: {
        address: 'QuickFix Garage, Port Elizabeth'
      },
      subject: {
        id: 'M002',
        name: 'QuickFix Garage',
        contactPerson: 'Sarah Johnson',
        phone: '************'
      },
      inspectionReason: 'Compliance Audit',
      estimatedDuration: 120,
      notes: 'Follow up on previous non-compliance issues'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'In Progress': return 'bg-blue-100 text-blue-800';
      case 'Scheduled': return 'bg-gray-100 text-gray-800';
      case 'Overdue': return 'bg-red-100 text-red-800';
      case 'Cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Completed': return <CheckCircle className="h-4 w-4" />;
      case 'In Progress': return <Clock className="h-4 w-4" />;
      case 'Scheduled': return <Calendar className="h-4 w-4" />;
      case 'Overdue': return <AlertTriangle className="h-4 w-4" />;
      case 'Cancelled': return <AlertTriangle className="h-4 w-4" />;
      default: return <ClipboardCheck className="h-4 w-4" />;
    }
  };

  const filteredInspections = inspections.filter(inspection => {
    const matchesSearch = 
      inspection.subject.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      inspection.subject.registration?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      inspection.location.address.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || inspection.status === statusFilter;
    const matchesType = typeFilter === 'all' || inspection.type === typeFilter;
    
    const matchesDate = dateFilter === 'all' || (() => {
      const inspectionDate = new Date(inspection.scheduledDate);
      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(today.getDate() + 1);
      
      switch (dateFilter) {
        case 'today': return inspectionDate.toDateString() === today.toDateString();
        case 'tomorrow': return inspectionDate.toDateString() === tomorrow.toDateString();
        case 'week': return inspectionDate <= new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
        default: return true;
      }
    })();
    
    return matchesSearch && matchesStatus && matchesType && matchesDate;
  });

  const calculateSummary = () => {
    const today = new Date().toDateString();
    const todayInspections = inspections.filter(i => new Date(i.scheduledDate).toDateString() === today);
    
    return {
      total: inspections.length,
      today: todayInspections.length,
      completed: inspections.filter(i => i.status === 'Completed').length,
      overdue: inspections.filter(i => i.status === 'Overdue').length,
      inProgress: inspections.filter(i => i.status === 'In Progress').length
    };
  };

  const { total, today, completed, overdue, inProgress } = calculateSummary();

  const navigate = useNavigate();
  
  const handleStartInspection = (inspectionId: string) => {
    dispatch(addNotification({
      type: 'success',
      message: 'Inspection Started',
      details: 'You can now begin the inspection checklist.'
    }));
    // Use React Router navigate for client-side routing
    navigate(`/inspector/checklist/${inspectionId}`);
  };

  const handleViewReport = (reportId: string) => {
    dispatch(addNotification({
      type: 'info',
      message: 'Opening Report',
      details: 'Loading inspection report...'
    }));
  };

  const handleNavigate = (inspection: Inspection) => {
    if (inspection.location.coordinates) {
      const { lat, lng } = inspection.location.coordinates;
      window.open(`https://maps.google.com?q=${lat},${lng}`, '_blank');
      dispatch(addNotification({
        type: 'info',
        message: 'Navigation Started',
        details: 'Opening directions in Google Maps.'
      }));
    }
  };

  const handleCall = (phone: string) => {
    window.open(`tel:${phone}`);
    dispatch(addNotification({
      type: 'info',
      message: 'Calling Contact',
      details: `Dialing ${phone}...`
    }));
  };

  return (
    <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8 space-y-4 sm:space-y-6 max-w-7xl">
      {/* Header */}
      <div className="text-center sm:text-left">
        <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">Inspector Dashboard</h1>
        <p className="text-sm sm:text-base text-gray-600">Manage vehicle and merchant inspections</p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4">
        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="text-center">
              <ClipboardCheck className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600 mx-auto mb-1 sm:mb-2" />
              <p className="text-xs font-medium text-gray-600">Total</p>
              <p className="text-base sm:text-lg font-bold text-gray-900">{total}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="text-center">
              <Calendar className="h-5 w-5 sm:h-6 sm:w-6 text-green-600 mx-auto mb-1 sm:mb-2" />
              <p className="text-xs font-medium text-gray-600">Today</p>
              <p className="text-base sm:text-lg font-bold text-green-600">{today}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="text-center">
              <Clock className="h-5 w-5 sm:h-6 sm:w-6 text-yellow-600 mx-auto mb-1 sm:mb-2" />
              <p className="text-xs font-medium text-gray-600">In Progress</p>
              <p className="text-base sm:text-lg font-bold text-yellow-600">{inProgress}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <CheckCircle className="h-6 w-6 text-blue-600 mx-auto mb-2" />
              <p className="text-xs font-medium text-gray-600">Completed</p>
              <p className="text-lg font-bold text-blue-600">{completed}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <AlertTriangle className="h-6 w-6 text-red-600 mx-auto mb-2" />
              <p className="text-xs font-medium text-gray-600">Overdue</p>
              <p className="text-lg font-bold text-red-600">{overdue}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search inspections..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="Scheduled">Scheduled</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                  <SelectItem value="Completed">Completed</SelectItem>
                  <SelectItem value="Overdue">Overdue</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="vehicle">Vehicle</SelectItem>
                  <SelectItem value="merchant">Merchant</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={dateFilter} onValueChange={setDateFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Date" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="tomorrow">Tomorrow</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="all">All Dates</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Inspections List */}
      <div className="space-y-4">
        {filteredInspections.map((inspection) => (
          <Card key={inspection.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="space-y-4">
                {/* Header */}
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      {inspection.type === 'vehicle' ? (
                        <Car className="h-5 w-5 text-blue-600" />
                      ) : (
                        <Building className="h-5 w-5 text-purple-600" />
                      )}
                      <h3 className="font-semibold">{inspection.subject.name}</h3>
                      {inspection.subject.registration && (
                        <Badge variant="outline">{inspection.subject.registration}</Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{inspection.inspectionReason}</p>
                  </div>
                  
                  <div className="flex flex-col items-end space-y-1">
                    <Badge className={getStatusColor(inspection.status)}>
                      {getStatusIcon(inspection.status)}
                      <span className="ml-1">{inspection.status}</span>
                    </Badge>
                    <Badge className={getPriorityColor(inspection.priority)}>
                      {inspection.priority}
                    </Badge>
                  </div>
                </div>

                {/* Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span>{new Date(inspection.scheduledDate).toLocaleDateString()} at {inspection.scheduledTime}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span>{inspection.estimatedDuration} minutes</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      <span className="truncate">{inspection.location.address}</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    {inspection.subject.contactPerson && (
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4 text-gray-400" />
                        <span>{inspection.subject.contactPerson}</span>
                      </div>
                    )}
                    <div className="flex items-center space-x-2">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <span>{inspection.subject.phone}</span>
                    </div>
                    {inspection.completedDate && (
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-green-600">
                          Completed: {new Date(inspection.completedDate).toLocaleDateString()}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Notes */}
                {inspection.notes && (
                  <div className="p-3 bg-gray-50 rounded text-sm">
                    <strong>Notes:</strong> {inspection.notes}
                  </div>
                )}

                {/* Actions */}
                <div className="flex flex-wrap gap-2">
                  {inspection.status === 'Scheduled' && (
                    <Button size="sm" onClick={() => handleStartInspection(inspection.id)}>
                      <ClipboardCheck className="h-4 w-4 mr-2" />
                      Start Inspection
                    </Button>
                  )}
                  
                  {inspection.status === 'In Progress' && (
                    <Button size="sm" variant="outline" onClick={() => handleStartInspection(inspection.id)}>
                      <ClipboardCheck className="h-4 w-4 mr-2" />
                      Continue
                    </Button>
                  )}
                  
                  {inspection.reportId && (
                    <Button size="sm" variant="outline" onClick={() => handleViewReport(inspection.reportId!)}>
                      <FileText className="h-4 w-4 mr-2" />
                      View Report
                    </Button>
                  )}
                  
                  {inspection.location.coordinates && (
                    <Button size="sm" variant="outline" onClick={() => handleNavigate(inspection)}>
                      <Navigation className="h-4 w-4 mr-2" />
                      Navigate
                    </Button>
                  )}
                  
                  <Button size="sm" variant="outline" onClick={() => handleCall(inspection.subject.phone)}>
                    <Phone className="h-4 w-4 mr-2" />
                    Call
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredInspections.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <ClipboardCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No inspections found</h3>
            <p className="text-gray-600">
              {searchTerm || statusFilter !== 'all' || typeFilter !== 'all' || dateFilter !== 'today'
                ? 'Try adjusting your search criteria or filters.'
                : 'No inspections scheduled for today.'}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default InspectorDashboard;


