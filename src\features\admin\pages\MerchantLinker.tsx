import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Store,
  Search,
  MapPin,
  Star,
  CheckCircle,
  ArrowLeft,
  Link,
  Unlink,
  Filter,
  AlertCircle,
  Wrench,
  Truck,
  Settings
} from 'lucide-react';

interface Merchant {
  id: string;
  name: string;
  region: string;
  specialty: string[];
  rating: number;
  status: 'active' | 'inactive' | 'pending';
  address: string;
  phone: string;
  email: string;
  certifications: string[];
  capacity: number;
  distance: number;
}

const MerchantLinker: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const customerData = location.state?.customerData;
  const fleetData = location.state?.fleetData;
  const documents = location.state?.documents;
  const hierarchy = location.state?.hierarchy;
  const users = location.state?.users;

  // Sample merchants data
  const [allMerchants] = useState<Merchant[]>([
    {
      id: '1',
      name: 'AutoCare Services',
      region: 'Gauteng',
      specialty: ['General Maintenance', 'Engine Repair', 'Electrical'],
      rating: 4.8,
      status: 'active',
      address: '123 Industrial Ave, Johannesburg',
      phone: '+27 11 123 4567',
      email: '<EMAIL>',
      certifications: ['ISO 9001', 'SABS Approved'],
      capacity: 50,
      distance: 5.2
    },
    {
      id: '2',
      name: 'Fleet Solutions Pro',
      region: 'Gauteng',
      specialty: ['Transmission', 'Brake Systems', 'Air Conditioning'],
      rating: 4.6,
      status: 'active',
      address: '456 Workshop St, Pretoria',
      phone: '+27 12 987 6543',
      email: '<EMAIL>',
      certifications: ['ISO 14001', 'Government Approved'],
      capacity: 75,
      distance: 12.8
    },
    {
      id: '3',
      name: 'Reliable Motors',
      region: 'Western Cape',
      specialty: ['Body Work', 'Paint Services', 'Glass Replacement'],
      rating: 4.4,
      status: 'active',
      address: '789 Service Rd, Cape Town',
      phone: '+27 21 555 0123',
      email: '<EMAIL>',
      certifications: ['SABS Approved'],
      capacity: 30,
      distance: 8.5
    },
    {
      id: '4',
      name: 'Express Auto Repair',
      region: 'KwaZulu-Natal',
      specialty: ['Quick Service', 'Oil Changes', 'Tire Services'],
      rating: 4.2,
      status: 'active',
      address: '321 Fast Lane, Durban',
      phone: '+27 31 444 5678',
      email: '<EMAIL>',
      certifications: ['Government Approved'],
      capacity: 40,
      distance: 15.3
    },
    {
      id: '5',
      name: 'Premium Vehicle Care',
      region: 'Gauteng',
      specialty: ['Luxury Vehicles', 'Diagnostics', 'Performance Tuning'],
      rating: 4.9,
      status: 'active',
      address: '654 Premium Blvd, Sandton',
      phone: '+27 11 888 9999',
      email: '<EMAIL>',
      certifications: ['ISO 9001', 'ISO 14001', 'Government Approved'],
      capacity: 25,
      distance: 7.1
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [regionFilter, setRegionFilter] = useState('');
  const [specialtyFilter, setSpecialtyFilter] = useState('');
  const [linkedMerchants, setLinkedMerchants] = useState<string[]>([]);

  // Filter merchants based on search and filters
  const filteredMerchants = allMerchants.filter(merchant => {
    const matchesSearch = merchant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         merchant.specialty.some(s => s.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesRegion = !regionFilter || merchant.region === regionFilter;
    const matchesSpecialty = !specialtyFilter || merchant.specialty.includes(specialtyFilter);
    
    return matchesSearch && matchesRegion && matchesSpecialty;
  });

  // Get unique regions and specialties for filters
  const regions = [...new Set(allMerchants.map(m => m.region))];
  const specialties = [...new Set(allMerchants.flatMap(m => m.specialty))];

  const toggleMerchantLink = (merchantId: string) => {
    setLinkedMerchants(prev => 
      prev.includes(merchantId)
        ? prev.filter(id => id !== merchantId)
        : [...prev, merchantId]
    );
  };

  const getLinkedMerchants = () => {
    return allMerchants.filter(merchant => linkedMerchants.includes(merchant.id));
  };

  const getSpecialtyIcon = (specialty: string) => {
    if (specialty.includes('Engine') || specialty.includes('Transmission')) {
      return <Settings className="h-4 w-4 text-blue-600" />;
    }
    if (specialty.includes('Body') || specialty.includes('Paint')) {
      return <Truck className="h-4 w-4 text-green-600" />;
    }
    return <Wrench className="h-4 w-4 text-gray-600" />;
  };

  const handleContinue = () => {
    navigate('/admin/customers/approval-workflow', {
      state: { 
        customerData,
        fleetData,
        documents,
        hierarchy,
        users,
        linkedMerchants: getLinkedMerchants()
      }
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Store className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">Merchant Linker</h1>
                <p className="text-sm text-gray-600">
                  {customerData?.departmentName || 'Customer'} - Link Service Providers
                </p>
              </div>
            </div>
            <Button variant="outline" onClick={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto space-y-6">
          
          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Link className="h-5 w-5 text-blue-600" />
                Link Service Providers
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-gray-600">
                  Select and link approved service providers (merchants) to your department. 
                  These merchants will be available for work order assignments and maintenance services.
                </p>
                
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    You can link multiple merchants to ensure service availability and competitive pricing. 
                    Merchants can be managed and updated later through the vendor management system.
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Available Merchants */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Available Merchants ({filteredMerchants.length})</CardTitle>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Filter className="h-4 w-4 mr-2" />
                        Filters
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Search and Filters */}
                  <div className="space-y-4 mb-6">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search merchants by name or specialty..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Select value={regionFilter} onValueChange={setRegionFilter}>
                        <SelectTrigger>
                          <SelectValue placeholder="Filter by region" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">All Regions</SelectItem>
                          {regions.map(region => (
                            <SelectItem key={region} value={region}>{region}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      
                      <Select value={specialtyFilter} onValueChange={setSpecialtyFilter}>
                        <SelectTrigger>
                          <SelectValue placeholder="Filter by specialty" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">All Specialties</SelectItem>
                          {specialties.map(specialty => (
                            <SelectItem key={specialty} value={specialty}>{specialty}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Merchants List */}
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {filteredMerchants.map((merchant) => (
                      <div key={merchant.id} className="border rounded-lg p-4 hover:shadow-sm transition-shadow">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="font-semibold text-gray-900">{merchant.name}</h3>
                              <div className="flex items-center gap-1">
                                <Star className="h-4 w-4 text-yellow-500 fill-current" />
                                <span className="text-sm text-gray-600">{merchant.rating}</span>
                              </div>
                            </div>
                            
                            <div className="space-y-2 text-sm text-gray-600">
                              <div className="flex items-center gap-2">
                                <MapPin className="h-4 w-4" />
                                <span>{merchant.region} • {merchant.distance}km away</span>
                              </div>
                              
                              <div className="flex flex-wrap gap-2">
                                {merchant.specialty.map((spec, index) => (
                                  <span key={index} className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                                    {getSpecialtyIcon(spec)}
                                    {spec}
                                  </span>
                                ))}
                              </div>
                              
                              <div className="flex items-center gap-4">
                                <span>Capacity: {merchant.capacity} vehicles</span>
                                <span>•</span>
                                <span>{merchant.certifications.join(', ')}</span>
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex flex-col items-end gap-2">
                            <Button
                              variant={linkedMerchants.includes(merchant.id) ? "default" : "outline"}
                              size="sm"
                              onClick={() => toggleMerchantLink(merchant.id)}
                            >
                              {linkedMerchants.includes(merchant.id) ? (
                                <>
                                  <CheckCircle className="h-4 w-4 mr-2" />
                                  Linked
                                </>
                              ) : (
                                <>
                                  <Link className="h-4 w-4 mr-2" />
                                  Link
                                </>
                              )}
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Linked Merchants Panel */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    Linked Merchants ({linkedMerchants.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {linkedMerchants.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <Store className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>No merchants linked yet</p>
                      <p className="text-sm">Select merchants from the list to link them</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {getLinkedMerchants().map((merchant) => (
                        <div key={merchant.id} className="border rounded-lg p-3">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium text-gray-900">{merchant.name}</h4>
                              <p className="text-sm text-gray-600">{merchant.region}</p>
                              <div className="flex items-center gap-1 mt-1">
                                <Star className="h-3 w-3 text-yellow-500 fill-current" />
                                <span className="text-xs text-gray-600">{merchant.rating}</span>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleMerchantLink(merchant.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Unlink className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Summary */}
              {linkedMerchants.length > 0 && (
                <Card className="mt-4">
                  <CardHeader>
                    <CardTitle className="text-lg">Summary</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Total Merchants:</span>
                        <span className="font-medium">{linkedMerchants.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Total Capacity:</span>
                        <span className="font-medium">
                          {getLinkedMerchants().reduce((sum, m) => sum + m.capacity, 0)} vehicles
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Avg. Rating:</span>
                        <span className="font-medium">
                          {(getLinkedMerchants().reduce((sum, m) => sum + m.rating, 0) / linkedMerchants.length).toFixed(1)}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-4">
            <Button variant="outline">
              Save Draft
            </Button>
            <Button 
              onClick={handleContinue}
              disabled={linkedMerchants.length === 0}
            >
              Continue to Workflow Setup
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MerchantLinker;
