import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { setNetworkStatus } from '../store/slices/errorSlice';

export const useNetworkStatus = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    const updateNetworkStatus = () => {
      dispatch(setNetworkStatus(navigator.onLine ? 'online' : 'offline'));
    };

    // Initial status
    updateNetworkStatus();

    // Listen for network changes
    window.addEventListener('online', updateNetworkStatus);
    window.addEventListener('offline', updateNetworkStatus);

    return () => {
      window.removeEventListener('online', updateNetworkStatus);
      window.removeEventListener('offline', updateNetworkStatus);
    };
  }, [dispatch]);
};