import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useAppDispatch } from '@/hooks/redux';
import { addNotification} from '@/store/slices/uiSlice';
import { 
  Camera,
  CheckCircle,
  X,
  Save,
  Send,
  ArrowLeft,
  MapPin,
  Clock,
  User,
  Car,
  Building
} from 'lucide-react';

interface ChecklistItem {
  id: string;
  category: string;
  item: string;
  status: 'pass' | 'fail' | 'na' | 'pending';
  notes?: string;
  photos?: string[];
  required: boolean;
}

interface InspectionData {
  id: string;
  type: 'vehicle' | 'merchant';
  subject: {
    name: string;
    registration?: string;
    contactPerson?: string;
  };
  location: string;
  startTime: string;
  inspector: string;
  checklist: ChecklistItem[];
}

const InspectionChecklist: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  
  const [currentInspection, setCurrentInspection] = useState<InspectionData>({
    id: id || 'INS-2025-001',
    type: 'vehicle',
    subject: {
      name: 'Toyota Hilux',
      registration: 'GP123ABC'
    },
    location: 'Department of Health, Pretoria',
    startTime: new Date().toISOString(),
    inspector: 'Inspector John Doe',
    checklist: [
      // Vehicle Safety Checklist
      { id: '1', category: 'Exterior', item: 'Body condition (dents, rust, damage)', status: 'pending', required: true },
      { id: '2', category: 'Exterior', item: 'Paint condition', status: 'pending', required: false },
      { id: '3', category: 'Exterior', item: 'License plate visibility and condition', status: 'pending', required: true },
      { id: '4', category: 'Exterior', item: 'Windscreen condition (cracks, chips)', status: 'pending', required: true },
      { id: '5', category: 'Exterior', item: 'Side and rear windows', status: 'pending', required: true },
      { id: '6', category: 'Exterior', item: 'Mirrors (side and rear view)', status: 'pending', required: true },
      
      { id: '7', category: 'Lights', item: 'Headlights (high and low beam)', status: 'pending', required: true },
      { id: '8', category: 'Lights', item: 'Tail lights', status: 'pending', required: true },
      { id: '9', category: 'Lights', item: 'Brake lights', status: 'pending', required: true },
      { id: '10', category: 'Lights', item: 'Indicators/turn signals', status: 'pending', required: true },
      { id: '11', category: 'Lights', item: 'Hazard lights', status: 'pending', required: true },
      { id: '12', category: 'Lights', item: 'Reverse lights', status: 'pending', required: true },
      
      { id: '13', category: 'Tires', item: 'Tire tread depth (minimum 1.6mm)', status: 'pending', required: true },
      { id: '14', category: 'Tires', item: 'Tire condition (cuts, bulges, wear)', status: 'pending', required: true },
      { id: '15', category: 'Tires', item: 'Tire pressure', status: 'pending', required: true },
      { id: '16', category: 'Tires', item: 'Spare tire condition', status: 'pending', required: true },
      
      { id: '17', category: 'Brakes', item: 'Brake pedal feel and travel', status: 'pending', required: true },
      { id: '18', category: 'Brakes', item: 'Handbrake operation', status: 'pending', required: true },
      { id: '19', category: 'Brakes', item: 'Brake fluid level', status: 'pending', required: true },
      
      { id: '20', category: 'Engine', item: 'Engine oil level and condition', status: 'pending', required: true },
      { id: '21', category: 'Engine', item: 'Coolant level', status: 'pending', required: true },
      { id: '22', category: 'Engine', item: 'Battery condition and terminals', status: 'pending', required: true },
      { id: '23', category: 'Engine', item: 'Belt condition', status: 'pending', required: false },
      
      { id: '24', category: 'Interior', item: 'Seat belts (all positions)', status: 'pending', required: true },
      { id: '25', category: 'Interior', item: 'Steering wheel and column', status: 'pending', required: true },
      { id: '26', category: 'Interior', item: 'Dashboard warning lights', status: 'pending', required: true },
      { id: '27', category: 'Interior', item: 'Horn operation', status: 'pending', required: true },
      { id: '28', category: 'Interior', item: 'Wipers and washers', status: 'pending', required: true },
      
      { id: '29', category: 'Safety Equipment', item: 'Fire extinguisher', status: 'pending', required: true },
      { id: '30', category: 'Safety Equipment', item: 'First aid kit', status: 'pending', required: true },
      { id: '31', category: 'Safety Equipment', item: 'Warning triangle', status: 'pending', required: true },
      { id: '32', category: 'Safety Equipment', item: 'Jack and wheel spanner', status: 'pending', required: true },
      
      { id: '33', category: 'Documentation', item: 'Vehicle registration papers', status: 'pending', required: true },
      { id: '34', category: 'Documentation', item: 'Insurance certificate', status: 'pending', required: true },
      { id: '35', category: 'Documentation', item: 'Roadworthy certificate', status: 'pending', required: true },
      { id: '36', category: 'Documentation', item: 'Service history/logbook', status: 'pending', required: false }
    ]
  });

  const [generalNotes, setGeneralNotes] = useState('');
  const [currentCategory, setCurrentCategory] = useState('Exterior');

  const categories = [...new Set(currentInspection.checklist.map(item => item.category))];

  const updateChecklistItem = (itemId: string, status: 'pass' | 'fail' | 'na', notes?: string) => {
    setCurrentInspection(prev => ({
      ...prev,
      checklist: prev.checklist.map(item =>
        item.id === itemId ? { ...item, status, notes } : item
      )
    }));

    if (status === 'fail') {
      dispatch(addNotification({
        type: 'warning',
        message: 'Item Failed',
        details: 'Please add notes and photos for documentation.'
      }));
    } else if (status === 'pass') {
      dispatch(addNotification({
        type: 'success',
        message: 'Item Passed',
        details: 'Inspection item marked as passed.'
      }));
    }
  };

  const addPhoto = (itemId: string, photoUrl: string) => {
    setCurrentInspection(prev => ({
      ...prev,
      checklist: prev.checklist.map(item =>
        item.id === itemId 
          ? { ...item, photos: [...(item.photos || []), photoUrl] }
          : item
      )
    }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass': return 'bg-green-100 text-green-800';
      case 'fail': return 'bg-red-100 text-red-800';
      case 'na': return 'bg-gray-100 text-gray-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return <CheckCircle className="h-4 w-4" />;
      case 'fail': return <X className="h-4 w-4" />;
      case 'na': return <X className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const calculateProgress = () => {
    const completed = currentInspection.checklist.filter(item => item.status !== 'pending').length;
    const total = currentInspection.checklist.length;
    return { completed, total, percentage: Math.round((completed / total) * 100) };
  };

  const { completed, total, percentage } = calculateProgress();

  const handleSaveDraft = () => {
    // Auto-save without modal
    dispatch(addNotification({
      type: 'success',
      message: 'Draft Saved',
      details: 'Your inspection progress has been saved.'
    }));
  };

  const handleSubmitInspection = () => {
    const requiredItems = currentInspection.checklist.filter(item => item.required);
    const incompleteRequired = requiredItems.filter(item => item.status === 'pending');
    
    if (incompleteRequired.length > 0) {
      dispatch(addNotification({
        type: 'error',
        message: 'Incomplete Inspection',
        details: `${incompleteRequired.length} required items must be completed.`
      }));
      return;
    }
    
    // Simple confirmation for final submission
    if (window.confirm('Submit this inspection? This action cannot be undone and will generate the final report.')) {
      dispatch(addNotification({
        type: 'success',
        message: 'Inspection Submitted',
        details: 'Inspection completed and report generated successfully.'
      }));
      navigate('/inspector/dashboard');
    }
  };

  const handleTakePhoto = (itemId: string) => {
    // Direct photo capture
    const mockPhotoUrl = `photo_${itemId}_${Date.now()}.jpg`;
    addPhoto(itemId, mockPhotoUrl);
    dispatch(addNotification({
      type: 'success',
      message: 'Photo Captured',
      details: 'Evidence photo saved to inspection item.'
    }));
  };

  const filteredChecklist = currentInspection.checklist.filter(item => item.category === currentCategory);

  return (
    <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8 space-y-4 sm:space-y-6 max-w-full">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 sm:mb-8 gap-3">
        <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
          <Button
            variant="outline"
            onClick={() => navigate('/inspector')}
            className="flex items-center space-x-2 self-start"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="hidden sm:inline">Back to Dashboard</span>
            <span className="sm:hidden">Back</span>
          </Button>
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Inspection Checklist</h1>
            <p className="text-sm sm:text-base text-gray-600">Complete vehicle inspection</p>
          </div>
        </div>
      </div>

      {/* Inspection Info */}
      <Card>
        <CardContent className="p-3 sm:p-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 text-sm">
            <div className="flex items-center space-x-2">
              {currentInspection.type === 'vehicle' ? (
                <Car className="h-4 w-4 text-blue-600 flex-shrink-0" />
              ) : (
                <Building className="h-4 w-4 text-purple-600 flex-shrink-0" />
              )}
              <span className="font-medium">{currentInspection.subject.name}</span>
            </div>
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-gray-400" />
              <span>{currentInspection.location}</span>
            </div>
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4 text-gray-400" />
              <span>{currentInspection.inspector}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Progress */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Progress</span>
            <span className="text-sm text-gray-600">{completed}/{total} items</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${percentage}%` }}
            />
          </div>
          <p className="text-xs text-gray-600 mt-1">{percentage}% complete</p>
        </CardContent>
      </Card>

      {/* Category Tabs */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-2">
            {categories.map(category => {
              const categoryItems = currentInspection.checklist.filter(item => item.category === category);
              const completedItems = categoryItems.filter(item => item.status !== 'pending').length;
              const isActive = currentCategory === category;
              
              return (
                <Button
                  key={category}
                  variant={isActive ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentCategory(category)}
                  className="relative"
                >
                  {category}
                  <Badge 
                    variant="secondary" 
                    className="ml-2 text-xs"
                  >
                    {completedItems}/{categoryItems.length}
                  </Badge>
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Checklist Items */}
      <div className="space-y-4">
        {filteredChecklist.map((item) => (
          <Card key={item.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="space-y-4">
                {/* Item Header */}
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className="font-medium">{item.item}</h3>
                      {item.required && (
                        <Badge variant="destructive" className="text-xs">Required</Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">Category: {item.category}</p>
                  </div>
                  
                  {item.status !== 'pending' && (
                    <Badge className={getStatusColor(item.status)}>
                      {getStatusIcon(item.status)}
                      <span className="ml-1 capitalize">{item.status}</span>
                    </Badge>
                  )}
                </div>

                {/* Status Buttons */}
                <div className="flex flex-wrap gap-2">
                  <Button
                    size="sm"
                    variant={item.status === 'pass' ? 'default' : 'outline'}
                    onClick={() => updateChecklistItem(item.id, 'pass')}
                    className="flex-1 md:flex-none"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Pass
                  </Button>
                  <Button
                    size="sm"
                    variant={item.status === 'fail' ? 'destructive' : 'outline'}
                    onClick={() => updateChecklistItem(item.id, 'fail')}
                    className="flex-1 md:flex-none"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Fail
                  </Button>
                  <Button
                    size="sm"
                    variant={item.status === 'na' ? 'secondary' : 'outline'}
                    onClick={() => updateChecklistItem(item.id, 'na')}
                    className="flex-1 md:flex-none"
                  >
                    <X className="h-4 w-4 mr-2" />
                    N/A
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleTakePhoto(item.id)}
                  >
                    <Camera className="h-4 w-4 mr-2" />
                    Photo
                  </Button>
                </div>

                {/* Photos */}
                {item.photos && item.photos.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {item.photos.map((_, index) => (
                      <div key={index} className="w-16 h-16 bg-gray-200 rounded border flex items-center justify-center">
                        <Camera className="h-6 w-6 text-gray-400" />
                      </div>
                    ))}
                  </div>
                )}

                {/* Notes */}
                {(item.status === 'fail' || item.notes) && (
                  <Textarea
                    placeholder="Add notes (required for failed items)..."
                    value={item.notes || ''}
                    onChange={(e) => updateChecklistItem(item.id, item.status === 'pending' ? 'fail' : item.status, e.target.value)}
                    className="min-h-[80px]"
                  />
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* General Notes */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">General Notes</CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="Add any general observations or recommendations..."
            value={generalNotes}
            onChange={(e) => setGeneralNotes(e.target.value)}
            className="min-h-[100px]"
          />
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex flex-col md:flex-row gap-4 sticky bottom-4 bg-white p-4 border rounded-lg shadow-lg">
        <Button variant="outline" onClick={handleSaveDraft} className="flex-1">
          <Save className="h-4 w-4 mr-2" />
          Save Draft
        </Button>
        <Button onClick={handleSubmitInspection} className="flex-1">
          <Send className="h-4 w-4 mr-2" />
          Submit Inspection
        </Button>
      </div>
    </div>
  );
};

export default InspectionChecklist;










