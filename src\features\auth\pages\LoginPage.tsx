import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useNavigate } from 'react-router-dom';
import logoImage from '@/assets/image1.png';
import { useAppDispatch, useAppSelector } from '@/hooks/redux';
import { loginStart, loginSuccess, loginFailure } from '@/store/slices/authSlice';
import { addNotification } from '@/store/slices/uiSlice';
import { authenticateUser, mockUsers } from '@/utils/mockAuth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Loader2, AlertCircle, User, Lock, Eye, EyeOff } from 'lucide-react';
import { Label } from '@radix-ui/react-label';
import { Link } from 'react-router-dom';

const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

type LoginFormData = z.infer<typeof loginSchema>;

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { isLoading } = useAppSelector((state) => state.auth);
  const [error, setError] = React.useState<string | null>(null);
  const [showPassword, setShowPassword] = React.useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginFormData) => {
    try {
      dispatch(loginStart());
      setError(null);

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Authenticate user with mock credentials
      const authenticatedUser = authenticateUser(data.email, data.password);

      if (authenticatedUser) {
        const mockToken = 'mock-jwt-token-' + Date.now();
        dispatch(loginSuccess({ user: authenticatedUser.user, token: mockToken }));
        dispatch(addNotification({
          type: 'success',
          message: 'Login Successful',
          details: `Welcome back, ${authenticatedUser.user.firstName}!`,
          duration: 5000 // Will auto-dismiss after 5 seconds
        }));
        navigate('/dashboard');
      } else {
        dispatch(loginFailure());
        dispatch(addNotification({
          type: 'error',
          message: 'Login Failed',
          details: 'Invalid email or password',
          duration: 5000
        }));
        setError('Invalid email or password');
      }
    } catch (err) {
      dispatch(loginFailure());
      dispatch(addNotification({
        type: 'error',
        message: 'Login Error',
        details: 'An error occurred during login',
        duration: 5000
      }));
      setError('An error occurred during login');
    }
  };

  const handleQuickLogin = (email: string, password: string) => {
    setValue('email', email);
    setValue('password', password);
  };

  const getRoleBadgeColor = (role: string) => {
    const colors = {
      fleet_manager: 'bg-blue-100 text-blue-800',
      transport_officer: 'bg-green-100 text-green-800',
      finance_officer: 'bg-purple-100 text-purple-800',
      driver: 'bg-yellow-100 text-yellow-800',
      auditor: 'bg-gray-100 text-gray-800',
      admin: 'bg-red-100 text-red-800',
    };
    return colors[role as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-3 sm:p-4 lg:p-6">
      <div className="w-full max-w-sm sm:max-w-md space-y-4 sm:space-y-6">
        {/* Header */}
        <div className="text-center space-y-3 sm:space-y-4">
          <div className="flex justify-center">
            <img src={logoImage} alt="Fleet Management Logo" className="h-16 sm:h-20 lg:h-24" />
          </div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-800 px-2">RT46-2026 Fleet Management</h1>
          <p className="text-sm sm:text-base text-gray-600 px-2">Welcome back to your fleet management portal</p>
        </div>

        {/* Login Form */}
        <Card className="shadow-md">
          <CardHeader className="space-y-3 sm:space-y-4 p-4 sm:p-6">
            <CardTitle className="text-lg sm:text-xl text-center">Welcome Back</CardTitle>
            <CardDescription className="text-center text-sm sm:text-base">
              Enter your credentials to access the system
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 p-4 sm:p-6 pt-0">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-sm">{error}</AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                  Email Address
                </Label>
                <div className="relative">
                  <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email"
                    className={`pl-10 h-11 sm:h-10 text-base sm:text-sm ${errors.email ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                    {...register('email')}
                  />
                </div>
                {errors.email && (
                  <p className="text-sm text-red-600">{errors.email.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                    Password
                  </Label>
                  <Link
                    to="/forgot-password"
                    className="text-xs sm:text-sm font-medium text-blue-600 hover:text-blue-500 hover:underline"
                  >
                    Forgot password?
                  </Link>
                </div>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter your password"
                    className={`pl-10 pr-10 h-11 sm:h-10 text-base sm:text-sm ${errors.password ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                    {...register('password')}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-3 text-gray-400 hover:text-gray-600 p-1"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
                {errors.password && (
                  <p className="text-sm text-red-600">{errors.password.message}</p>
                )}
              </div>

              <Button type="submit" className="w-full h-11 sm:h-10 text-base sm:text-sm" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing In...
                  </>
                ) : (
                  'Sign In'
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Demo Credentials */}
        <Card className="shadow-md">
          <Accordion type="single" collapsible>
            <AccordionItem value="demo-credentials" className="border-none">
              <AccordionTrigger className="px-4 sm:px-6 py-3 sm:py-4 hover:no-underline">
                <span className="text-blue-700 font-medium text-sm sm:text-base">Demo Login Credentials</span>
              </AccordionTrigger>
              <AccordionContent className="px-4 sm:px-6 pb-4">
                <p className="text-xs sm:text-sm text-gray-600 mb-3 sm:mb-4">
                  Click on any credential to auto-fill the login form:
                </p>
                <div className="space-y-2">
                  {mockUsers.map((mockUser) => (
                    <div
                      key={mockUser.user.id}
                      onClick={() => handleQuickLogin(mockUser.email, mockUser.password)}
                      className="p-2 sm:p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                    >
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2 gap-1 sm:gap-0">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                          <span className={`px-2 py-1 rounded-md text-xs font-medium w-fit ${getRoleBadgeColor(mockUser.user.role)}`}>
                            {mockUser.user.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </span>
                          <span className="text-xs sm:text-sm font-medium text-gray-800">
                            {mockUser.user.firstName} {mockUser.user.lastName}
                          </span>
                        </div>
                        <span className="text-xs text-gray-500">
                          {mockUser.user.permissions.length} permissions
                        </span>
                      </div>
                      <div className="text-xs text-gray-600 space-y-1">
                        <div className="break-all"><strong>Email:</strong> {mockUser.email}</div>
                        <div><strong>Password:</strong> <code className="bg-gray-100 px-1 rounded text-xs">{mockUser.password}</code></div>
                      </div>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-3 sm:mt-4 text-center">
                  * This is a demo system. In production, these credentials would be securely managed.
                </p>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </Card>

        {/* Create Account Link */}
        <div className="text-center pt-3 sm:pt-4 border-t">
          <p className="text-xs sm:text-sm text-gray-600">
            Don't have an account?{' '}
            <Link
              to="/signup"
              className="font-medium text-blue-600 hover:text-blue-500 hover:underline"
            >
              Create account
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;

