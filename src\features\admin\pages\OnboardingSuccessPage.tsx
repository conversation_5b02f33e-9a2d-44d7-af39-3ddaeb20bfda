import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Clock, Mail, Phone } from 'lucide-react';

const OnboardingSuccessPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  const { entityName, applicationId } = location.state || {};

  return (
    <div className="container mx-auto px-4 py-6 max-w-2xl">
      <Card className="w-full">
        <CardContent className="w-full pt-6">
          <div className="w-full text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <CardTitle className="text-2xl text-green-600">Application Submitted Successfully!</CardTitle>
          </div>
          <div className="text-center">
            <p className="text-gray-600 mb-2">
              Thank you for submitting your fleet management application.
            </p>
            <p className="font-medium">
              Application ID: <span className="text-blue-600">{applicationId}</span>
            </p>
            {entityName && (
              <p className="text-sm text-gray-500">
                Entity: {entityName}
              </p>
            )}
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-start gap-3">
              <Clock className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900">What happens next?</h4>
                <ul className="text-sm text-blue-800 mt-2 space-y-1">
                  <li>• Our admin team will review your application</li>
                  <li>• You'll receive an email confirmation within 24 hours</li>
                  <li>• Approval typically takes 3-5 business days</li>
                  <li>• Once approved, you'll receive setup instructions</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Need assistance?</h4>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-gray-500" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-gray-500" />
                <span>+27 12 345 6789</span>
              </div>
            </div>
          </div>

          <div className="flex justify-center">
            <Button onClick={() => navigate('/')}>
              Return to Home
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OnboardingSuccessPage;
