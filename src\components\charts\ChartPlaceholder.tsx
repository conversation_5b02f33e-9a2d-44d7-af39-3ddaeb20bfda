import React from 'react';
import { Bar<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TrendingUp } from 'lucide-react';

interface ChartPlaceholderProps {
  type: 'bar' | 'pie' | 'line';
  title: string;
  height?: string;
}

const ChartPlaceholder: React.FC<ChartPlaceholderProps> = ({ 
  type, 
  title, 
  height = 'h-64' 
}) => {
  const getIcon = () => {
    switch (type) {
      case 'bar': return <BarChart3 className="h-12 w-12 text-gray-400" />;
      case 'pie': return <PieChart className="h-12 w-12 text-gray-400" />;
      case 'line': return <TrendingUp className="h-12 w-12 text-gray-400" />;
      default: return <BarChart3 className="h-12 w-12 text-gray-400" />;
    }
  };

  return (
    <div className={`${height} flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-200`}>
      <div className="text-center">
        {getIcon()}
        <p className="text-gray-500 mt-2 font-medium">{title}</p>
        <p className="text-sm text-gray-400">Chart will be integrated here</p>
      </div>
    </div>
  );
};

export default ChartPlaceholder;