import { api } from '../api';
import type { 
  WorkOrderResponse, 
  WorkOrderRequest, 
  PaginatedResponse, 
  WorkOrderFilters 
} from '../../types/api';

export const workOrderApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get work orders with filtering
    getWorkOrders: builder.query<PaginatedResponse<WorkOrderResponse>, WorkOrderFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        
        params.append('page', String(filters.page || 1));
        params.append('limit', String(filters.limit || 20));
        
        if (filters.status) params.append('status', filters.status);
        if (filters.priority) params.append('priority', filters.priority);
        if (filters.category) params.append('category', filters.category);
        if (filters.vehicle_id) params.append('vehicle_id', filters.vehicle_id);
        if (filters.assigned_to) params.append('assigned_to', filters.assigned_to);
        if (filters.date_from) params.append('date_from', filters.date_from);
        if (filters.date_to) params.append('date_to', filters.date_to);
        
        return {
          url: '/work-orders',
          params: Object.fromEntries(params),
        };
      },
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: 'WorkOrder' as const, id })),
              { type: 'WorkOrder', id: 'LIST' },
            ]
          : [{ type: 'WorkOrder', id: 'LIST' }],
      transformResponse: (response: any): PaginatedResponse<WorkOrderResponse> => {
        if (Array.isArray(response)) {
          return {
            data: response,
            pagination: {
              page: 1,
              limit: response.length,
              total: response.length,
              totalPages: 1,
              hasNext: false,
              hasPrev: false,
            },
            timestamp: new Date().toISOString(),
          };
        }
        
        return {
          data: response.data || response.workOrders || [],
          pagination: response.pagination || {
            page: 1,
            limit: 20,
            total: response.total || 0,
            totalPages: Math.ceil((response.total || 0) / 20),
            hasNext: false,
            hasPrev: false,
          },
          message: response.message,
          timestamp: response.timestamp || new Date().toISOString(),
        };
      },
    }),

    // Get single work order
    getWorkOrder: builder.query<WorkOrderResponse, string>({
      query: (id) => `/work-orders/${id}`,
      providesTags: (result, error, id) => [{ type: 'WorkOrder', id }],
      transformResponse: (response: any): WorkOrderResponse => {
        return response.data || response;
      },
    }),

    // Create work order
    createWorkOrder: builder.mutation<WorkOrderResponse, WorkOrderRequest>({
      query: (workOrder) => ({
        url: '/work-orders',
        method: 'POST',
        body: workOrder,
      }),
      invalidatesTags: [
        { type: 'WorkOrder', id: 'LIST' },
        { type: 'Vehicle', id: 'LIST' }, // Vehicle status might change
      ],
      transformResponse: (response: any): WorkOrderResponse => {
        return response.data || response;
      },
    }),

    // Update work order
    updateWorkOrder: builder.mutation<WorkOrderResponse, { id: string; data: Partial<WorkOrderRequest> }>({
      query: ({ id, data }) => ({
        url: `/work-orders/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'WorkOrder', id },
        { type: 'WorkOrder', id: 'LIST' },
      ],
    }),

    // Update work order status
    updateWorkOrderStatus: builder.mutation<WorkOrderResponse, { id: string; status: string; notes?: string }>({
      query: ({ id, status, notes }) => ({
        url: `/work-orders/${id}/status`,
        method: 'PATCH',
        body: { status, notes },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'WorkOrder', id },
        { type: 'WorkOrder', id: 'LIST' },
      ],
    }),

    // Get work order statistics
    getWorkOrderStats: builder.query<{
      total: number;
      by_status: Record<string, number>;
      by_priority: Record<string, number>;
      by_category: Record<string, number>;
    }, { date_from?: string; date_to?: string }>({
      query: (filters = {}) => ({
        url: '/work-orders/stats',
        params: filters,
      }),
      providesTags: [{ type: 'WorkOrder', id: 'STATS' }],
    }),
  }),
});

export const {
  useGetWorkOrdersQuery,
  useGetWorkOrderQuery,
  useCreateWorkOrderMutation,
  useUpdateWorkOrderMutation,
  useUpdateWorkOrderStatusMutation,
  useGetWorkOrderStatsQuery,
  useLazyGetWorkOrdersQuery,
} = workOrderApi;
