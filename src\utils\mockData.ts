import type { Vehicle } from '@/types/vehicle';
import type { Vendor } from '@/types/vendor';

export const mockVehicles: Vehicle[] = [
  {
    id: '1',
    registrationNumber: 'GP123ABC',
    make: 'Toyota',
    model: 'Hilux',
    year: 2022,
    vin: '1HGBH41JXMN109186',
    engineNumber: 'ENG123456',
    fuelType: 'diesel',
    category: 'truck',
    department: 'Public Works',
    status: 'active',
    mileage: 45000,
    lastServiceDate: '2024-01-15',
    nextServiceDate: '2024-04-15',
    insuranceExpiry: '2024-12-31',
    licenseExpiry: '2024-11-30',
    purchaseDate: '2022-03-15',
    purchasePrice: 450000,
    currentValue: 380000,
    assignedDriver: 'John Doe',
    location: 'Johannesburg Depot',
    createdAt: '2022-03-15T10:00:00Z',
    updatedAt: '2024-01-15T14:30:00Z',
  },
  {
    id: '2',
    registrationNumber: 'CT456DEF',
    make: 'Ford',
    model: 'Ranger',
    year: 2021,
    vin: '2HGBH41JXMN109187',
    engineNumber: 'ENG789012',
    fuelType: 'diesel',
    category: 'truck',
    department: 'Health',
    status: 'maintenance',
    mileage: 67000,
    lastServiceDate: '2024-01-10',
    nextServiceDate: '2024-04-10',
    insuranceExpiry: '2024-10-31',
    licenseExpiry: '2024-09-30',
    purchaseDate: '2021-05-20',
    purchasePrice: 420000,
    currentValue: 320000,
    assignedDriver: 'Jane Smith',
    location: 'Cape Town Depot',
    createdAt: '2021-05-20T09:00:00Z',
    updatedAt: '2024-01-10T11:15:00Z',
  },
];

export const mockVendors: Vendor[] = [
  {
    id: '1',
    name: 'AutoCare Services',
    contactPerson: 'John Smith',
    email: '<EMAIL>',
    phone: '************',
    address: '123 Church Street, Pretoria Central, 0002',
    city: 'Pretoria',
    province: 'Gauteng',
    postalCode: '0002',
    registrationNumber: 'REG123456',
    taxNumber: 'TAX789012',
    services: [
      'Engine Repair',
      'Brake Service'
    ],
    status: 'active',
    rating: 4.8,
    totalJobs: 156,
    completedJobs: 148,
    averageCompletionTime: 3.2,
    averageCost: 8500,
    certifications: ['ISO 9001', 'MIWA Certified'],
    specializations: ['Engine Repair', 'Brake Service'],
    bbbeeLevel: 4,
    insuranceExpiry: '2024-12-31',
    registrationDate: '2023-03-15',
    lastJobDate: '2025-01-10',
    complianceScore: 95,
    totalContracts: 25,
    createdAt: '2023-03-15T10:00:00Z',
    updatedAt: '2025-01-10T14:30:00Z',
  },
  {
    id: '2',
    name: 'Fleet Solutions SA',
    contactPerson: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '************',
    address: '456 Main Road, Johannesburg, 2000',
    city: 'Johannesburg',
    province: 'Gauteng',
    postalCode: '2000',
    registrationNumber: 'REG654321',
    taxNumber: 'TAX210987',
    services: [
      { id: '3', name: 'Fleet Maintenance', category: 'maintenance', hourlyRate: 400 },
      { id: '4', name: 'Emergency Repairs', category: 'repair', hourlyRate: 550 }
    ],
    status: 'active',
    rating: 4.5,
    totalJobs: 89,
    completedJobs: 82,
    averageCompletionTime: 2.8,
    averageCost: 7200,
    certifications: ['Toyota Approved', 'Ford Certified'],
    specializations: ['Fleet Maintenance', 'Emergency Repairs'],
    bbbeeLevel: 3,
    insuranceExpiry: '2024-11-30',
    registrationDate: '2023-06-20',
    lastJobDate: '2025-01-08',
    complianceScore: 88,
    totalContracts: 18,
    createdAt: '2023-06-20T09:00:00Z',
    updatedAt: '2025-01-08T16:45:00Z',
  }
];

// Mock API responses for development
export const mockApiResponses = {
  vehicles: {
    list: {
      vehicles: mockVehicles,
      total: mockVehicles.length,
    },
  },
};
