
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Car, 
  Calendar, 
  MapPin, 
  User, 
  Fuel, 
  Settings,
  Eye,
  Edit,
  AlertTriangle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { Vehicle } from '@/types/vehicle';

interface VehicleCardProps {
  vehicle: Vehicle;
  onView?: (id: string) => void;
  onEdit?: (id: string) => void;
  onScheduleService?: (id: string) => void;
  className?: string;
}

export const VehicleCard: React.FC<VehicleCardProps> = ({
  vehicle,
  onView,
  onEdit,
  onScheduleService,
  className
}) => {
  const getStatusColor = (status: Vehicle['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800';
      case 'retired':
        return 'bg-gray-100 text-gray-800';
      case 'accident':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const isServiceDue = () => {
    if (!vehicle.nextServiceDate) return false;
    const nextService = new Date(vehicle.nextServiceDate);
    const today = new Date();
    const daysUntilService = Math.ceil((nextService.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilService <= 30;
  };

  return (
    <Card className={cn("w-full hover:shadow-md transition-shadow", className)}>
      <CardHeader className="pb-2 sm:pb-3 p-4 sm:p-6">
        <div className="flex items-start justify-between w-full gap-2">
          <div className="min-w-0 flex-1">
            <CardTitle className="text-base sm:text-lg font-semibold truncate">
              {vehicle.registrationNumber}
            </CardTitle>
            <p className="text-xs sm:text-sm text-gray-600 truncate">
              {vehicle.make} {vehicle.model} ({vehicle.year})
            </p>
          </div>
          <Badge className={`${getStatusColor(vehicle.status)} text-xs flex-shrink-0`}>
            {vehicle.status}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-3 sm:space-y-4 p-4 sm:p-6 pt-0">
        {/* Vehicle Info */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3 text-xs sm:text-sm">
          <div className="flex items-center gap-2">
            <Car className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500 flex-shrink-0" />
            <span className="text-gray-600 truncate">{vehicle.category}</span>
          </div>
          <div className="flex items-center gap-2">
            <Fuel className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500 flex-shrink-0" />
            <span className="text-gray-600 truncate">{vehicle.fuelType}</span>
          </div>
          <div className="flex items-center gap-2">
            <MapPin className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500 flex-shrink-0" />
            <span className="text-gray-600 truncate">{vehicle.location}</span>
          </div>
          <div className="flex items-center gap-2">
            <User className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500 flex-shrink-0" />
            <span className="text-gray-600 truncate">{vehicle.assignedDriver || 'Unassigned'}</span>
          </div>
        </div>

        {/* Department */}
        <div className="text-xs sm:text-sm">
          <span className="font-medium text-gray-700">Department: </span>
          <span className="text-gray-600">{vehicle.department}</span>
        </div>

        {/* Mileage */}
        <div className="text-xs sm:text-sm">
          <span className="font-medium text-gray-700">Mileage: </span>
          <span className="text-gray-600">{vehicle.mileage.toLocaleString()} km</span>
        </div>

        {/* Service Alert */}
        {isServiceDue() && (
          <div className="flex items-center gap-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
            <AlertTriangle className="h-3 w-3 sm:h-4 sm:w-4 text-yellow-600 flex-shrink-0" />
            <span className="text-xs sm:text-sm text-yellow-800">Service due soon</span>
          </div>
        )}

        {/* Next Service */}
        {vehicle.nextServiceDate && (
          <div className="flex items-center gap-2 text-xs sm:text-sm">
            <Calendar className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500 flex-shrink-0" />
            <span className="text-gray-600">
              Next service: {new Date(vehicle.nextServiceDate).toLocaleDateString()}
            </span>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-1 sm:gap-2 pt-2">
          {onView && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onView(vehicle.id)}
              className="flex-1 h-8 sm:h-9 text-xs sm:text-sm px-2 sm:px-3"
            >
              <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
              <span className="hidden sm:inline">View</span>
              <span className="sm:hidden">View</span>
            </Button>
          )}
          {onEdit && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit(vehicle.id)}
              className="flex-1 h-8 sm:h-9 text-xs sm:text-sm px-2 sm:px-3"
            >
              <Edit className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
              <span className="hidden sm:inline">Edit</span>
              <span className="sm:hidden">Edit</span>
            </Button>
          )}
          {onScheduleService && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onScheduleService(vehicle.id)}
              className="flex-1 h-8 sm:h-9 text-xs sm:text-sm px-2 sm:px-3"
            >
              <Settings className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
              <span className="hidden sm:inline">Service</span>
              <span className="sm:hidden">Svc</span>
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};


