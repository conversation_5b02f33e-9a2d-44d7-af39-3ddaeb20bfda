import { api } from '../api';
import type { PaginatedResponse } from '../../types/api';

export interface Driver {
  id: string;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  license_number: string;
  license_expiry: string;
  license_class: string;
  department: string;
  status: 'active' | 'inactive' | 'suspended';
  assigned_vehicles: string[];
  total_trips: number;
  total_distance: number;
  safety_score: number;
  created_at: string;
  updated_at: string;
}

export interface Trip {
  id: string;
  driver_id: string;
  vehicle_id: string;
  start_location: string;
  end_location: string;
  start_time: string;
  end_time?: string;
  distance: number;
  fuel_consumed?: number;
  status: 'in_progress' | 'completed' | 'cancelled';
  purpose: string;
  odometer_start: number;
  odometer_end?: number;
  created_at: string;
}

export interface DriverFilters {
  page?: number;
  limit?: number;
  department?: string;
  status?: string;
  search?: string;
}

export const driverApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get drivers
    getDrivers: builder.query<PaginatedResponse<Driver>, DriverFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        
        params.append('page', String(filters.page || 1));
        params.append('limit', String(filters.limit || 20));
        
        if (filters.department) params.append('department', filters.department);
        if (filters.status) params.append('status', filters.status);
        if (filters.search) params.append('search', filters.search);
        
        return {
          url: '/drivers',
          params: Object.fromEntries(params),
        };
      },
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: 'Driver' as const, id })),
              { type: 'Driver', id: 'LIST' },
            ]
          : [{ type: 'Driver', id: 'LIST' }],
    }),

    // Get driver trips
    getDriverTrips: builder.query<PaginatedResponse<Trip>, { driver_id: string; page?: number; limit?: number }>({
      query: ({ driver_id, page = 1, limit = 20 }) => ({
        url: `/drivers/${driver_id}/trips`,
        params: { page, limit },
      }),
      providesTags: (result, error, { driver_id }) => [
        { type: 'Driver', id: `${driver_id}_TRIPS` },
      ],
    }),

    // Start trip
    startTrip: builder.mutation<Trip, {
      driver_id: string;
      vehicle_id: string;
      start_location: string;
      purpose: string;
      odometer_start: number;
    }>({
      query: (tripData) => ({
        url: '/trips/start',
        method: 'POST',
        body: tripData,
      }),
      invalidatesTags: (result, error, { driver_id }) => [
        { type: 'Driver', id: `${driver_id}_TRIPS` },
      ],
    }),

    // End trip
    endTrip: builder.mutation<Trip, {
      trip_id: string;
      end_location: string;
      odometer_end: number;
      fuel_consumed?: number;
    }>({
      query: ({ trip_id, ...data }) => ({
        url: `/trips/${trip_id}/end`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'Driver', id: 'LIST' }],
    }),
  }),
});

export const {
  useGetDriversQuery,
  useGetDriverTripsQuery,
  useStartTripMutation,
  useEndTripMutation,
} = driverApi;