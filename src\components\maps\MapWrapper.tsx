import React from 'react';
import { Wrapper, Status } from '@googlemaps/react-wrapper';
import { Box, CircularProgress, Typography } from '@mui/material';
import GoogleMap from './GoogleMap';

interface MapWrapperProps {
  center: { lat: number; lng: number };
  zoom: number;
  vehicles: Array<{
    id: string;
    registrationNumber: string;
    lat: number;
    lng: number;
    status: 'moving' | 'idling' | 'stopped';
    speed: number;
    driver: string;
    lastUpdate: string;
    department: string;
  }>;
  onVehicleClick: (vehicle: any) => void;
  onMapClick?: (lat: number, lng: number) => void;
}

const render = (status: Status) => {
  switch (status) {
    case Status.LOADING:
      return (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%'
          }}
        >
          <CircularProgress />
          <Typography sx={{ mt: 2 }}>Loading Google Maps...</Typography>
        </Box>
      );
    case Status.FAILURE:
      return (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            bgcolor: '#f5f5f5'
          }}
        >
          <Typography color="error">Failed to load Google Maps</Typography>
        </Box>
      );
    case Status.SUCCESS:
      return null;
  }
};

const MapWrapper: React.FC<MapWrapperProps> = (props) => {
  const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '';

  if (!apiKey) {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          bgcolor: '#f5f5f5'
        }}
      >
        <Typography color="error">
          Google Maps API key not configured
        </Typography>
      </Box>
    );
  }

  return (
    <Wrapper apiKey={apiKey} render={render}>
      <GoogleMap {...props} />
    </Wrapper>
  );
};

export default MapWrapper;