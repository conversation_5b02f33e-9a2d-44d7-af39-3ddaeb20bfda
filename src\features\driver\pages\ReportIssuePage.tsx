import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Camera, MapPin, AlertTriangle, Clock } from 'lucide-react';
import { useNotifications } from '@/hooks/useNotifications';
import { useAppDispatch } from '@/hooks/redux';
import { openModal } from '@/store/slices/uiSlice';

interface IssueFormData {
  type: string;
  severity: string;
  description: string;
  location: string;
  vehicleCondition: string;
  photos: File[];
}

const ReportIssuePage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { showSuccess, showError, showWarning, notifications } = useNotifications();

  const [formData, setFormData] = useState<IssueFormData>({
    type: '',
    severity: '',
    description: '',
    location: '',
    vehicleCondition: 'driveable',
    photos: []
  });

  const issueTypes = [
    { id: 'breakdown', name: 'Vehicle Breakdown', icon: '🔧' },
    { id: 'accident', name: 'Accident/Collision', icon: '🚗' },
    { id: 'mechanical', name: 'Mechanical Issue', icon: '⚙️' },
    { id: 'electrical', name: 'Electrical Problem', icon: '⚡' },
    { id: 'tire', name: 'Tire Issue', icon: '🛞' },
    { id: 'other', name: 'Other Issue', icon: '❓' }
  ];

  const severityLevels = [
    { id: 'low', name: 'Low', color: 'text-green-600 bg-green-100', description: 'Minor issue, can continue' },
    { id: 'medium', name: 'Medium', color: 'text-yellow-600 bg-yellow-100', description: 'Needs attention soon' },
    { id: 'high', name: 'High', color: 'text-red-600 bg-red-100', description: 'Urgent, safety concern' },
    { id: 'critical', name: 'Critical', color: 'text-red-800 bg-red-200', description: 'Emergency, cannot drive' }
  ];

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newPhotos = Array.from(e.target.files);
      setFormData(prev => ({
        ...prev,
        photos: [...prev.photos, ...newPhotos].slice(0, 5) // Max 5 photos
      }));
    }
  };

  const removePhoto = (index: number) => {
    setFormData(prev => ({
      ...prev,
      photos: prev.photos.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.type) {
      showError('Validation Error', 'Please select an issue type');
      return;
    }

    if (!formData.severity) {
      showError('Validation Error', 'Please select issue severity');
      return;
    }

    if (!formData.description.trim()) {
      showError('Validation Error', 'Please provide a description of the issue');
      return;
    }

    // Handle critical issues immediately
    if (formData.severity === 'critical') {
      dispatch(openModal({
        id: 'critical-issue',
        type: 'confirm',
        props: {
          title: 'Critical Issue Reported',
          message: 'This is a critical safety issue. Emergency services and fleet control will be notified immediately. Do not attempt to drive the vehicle.',
          confirmText: 'Confirm Emergency',
          cancelText: 'Review Report',
          onConfirm: () => {
            console.log('Critical issue reported:', formData);
            showError('Emergency Alert', 'Critical issue reported. Fleet control and emergency services notified.');
            notifications.slaBreachWarning('EMERGENCY-' + Date.now());
            navigate('/driver/dashboard');
          }
        }
      }));
      return;
    }

    // Regular issue submission
    dispatch(openModal({
      id: 'confirm-issue-report',
      type: 'confirm',
      props: {
        title: 'Submit Issue Report',
        message: `Report ${formData.type} issue with ${formData.severity} severity?`,
        confirmText: 'Submit Report',
        cancelText: 'Cancel',
        onConfirm: () => {
          console.log('Issue reported:', formData);
          
          // Show appropriate notification based on severity
          if (formData.severity === 'high') {
            showWarning('High Priority Issue', 'Issue reported. Fleet control will contact you shortly.');
          } else {
            showSuccess('Issue Reported', 'Your issue report has been submitted successfully.');
          }
          
          navigate('/driver/dashboard');
        }
      }
    }));
  };

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setFormData(prev => ({
            ...prev,
            location: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`
          }));
        },
        (error) => {
          console.error('Error getting location:', error);
          alert('Could not get current location. Please enter manually.');
        }
      );
    }
  };

  // Auto-show warning for unsafe vehicle condition
  useEffect(() => {
    if (formData.vehicleCondition === 'unsafe') {
      showError('Safety Warning', 'Do not drive the vehicle if it is unsafe. Contact fleet control immediately.');
    }
  }, [formData.vehicleCondition]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-red-600 text-white p-3 sm:p-4">
        <div className="flex items-center space-x-2 sm:space-x-3">
          <button onClick={() => navigate(-1)} className="p-1 flex-shrink-0">
            <ArrowLeft className="h-5 w-5 sm:h-6 sm:w-6" />
          </button>
          <AlertTriangle className="h-5 w-5 sm:h-6 sm:w-6 flex-shrink-0" />
          <h1 className="text-lg sm:text-xl font-bold truncate">Report Issue</h1>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="p-3 sm:p-4 space-y-4 sm:space-y-6">
        {/* Issue Type */}
        <div>
          <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-2 sm:mb-3">
            What type of issue are you experiencing?
          </label>
          <div className="grid grid-cols-2 gap-3">
            {issueTypes.map((type) => (
              <button
                key={type.id}
                type="button"
                onClick={() => setFormData({ ...formData, type: type.id })}
                className={`p-4 rounded-lg border-2 text-left ${
                  formData.type === type.id
                    ? 'border-red-500 bg-red-50'
                    : 'border-gray-200 bg-white'
                }`}
              >
                <div className="text-2xl mb-1">{type.icon}</div>
                <span className="text-sm font-medium">{type.name}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Severity */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            How severe is the issue?
          </label>
          <div className="space-y-2">
            {severityLevels.map((level) => (
              <button
                key={level.id}
                type="button"
                onClick={() => setFormData({ ...formData, severity: level.id })}
                className={`w-full p-3 rounded-lg border-2 text-left ${
                  formData.severity === level.id
                    ? 'border-red-500 bg-red-50'
                    : 'border-gray-200 bg-white'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${level.color}`}>
                      {level.name}
                    </span>
                    <p className="text-sm text-gray-600 mt-1">{level.description}</p>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Describe the issue in detail
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            placeholder="Please provide as much detail as possible about what happened, when it started, any warning signs, etc."
            rows={4}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
            required
          />
        </div>

        {/* Location */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Current Location
          </label>
          <div className="flex space-x-2">
            <input
              type="text"
              value={formData.location}
              onChange={(e) => setFormData({ ...formData, location: e.target.value })}
              placeholder="Enter location or use GPS"
              className="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
              required
            />
            <button
              type="button"
              onClick={getCurrentLocation}
              className="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <MapPin className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Vehicle Condition */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Can you still drive the vehicle safely?
          </label>
          <div className="space-y-2">
            {[
              { id: 'driveable', name: 'Yes, vehicle is driveable', color: 'border-green-500 bg-green-50' },
              { id: 'limited', name: 'Limited driving (slow/short distance)', color: 'border-yellow-500 bg-yellow-50' },
              { id: 'unsafe', name: 'No, unsafe to drive', color: 'border-red-500 bg-red-50' }
            ].map((condition) => (
              <button
                key={condition.id}
                type="button"
                onClick={() => setFormData({ ...formData, vehicleCondition: condition.id })}
                className={`w-full p-3 rounded-lg border-2 text-left ${
                  formData.vehicleCondition === condition.id
                    ? condition.color
                    : 'border-gray-200 bg-white'
                }`}
              >
                {condition.name}
              </button>
            ))}
          </div>
        </div>

        {/* Photos */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Add Photos (Optional)
          </label>
          <div className="space-y-3">
            <input
              type="file"
              accept="image/*"
              multiple
              onChange={handlePhotoUpload}
              className="hidden"
              id="photo-upload"
            />
            <label
              htmlFor="photo-upload"
              className="flex items-center justify-center w-full p-4 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-gray-400"
            >
              <div className="text-center">
                <Camera className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <span className="text-sm text-gray-600">
                  Tap to add photos ({formData.photos.length}/5)
                </span>
              </div>
            </label>

            {formData.photos.length > 0 && (
              <div className="grid grid-cols-3 gap-2">
                {formData.photos.map((photo, index) => (
                  <div key={index} className="relative">
                    <img
                      src={URL.createObjectURL(photo)}
                      alt={`Issue photo ${index + 1}`}
                      className="w-full h-20 object-cover rounded-lg"
                    />
                    <button
                      type="button"
                      onClick={() => removePhoto(index)}
                      className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Emergency Notice */}
        {formData.severity === 'critical' && (
          <div className="bg-red-100 border border-red-400 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <span className="font-medium text-red-800">Emergency Situation</span>
            </div>
            <p className="text-sm text-red-700 mb-3">
              For critical issues, please also call emergency services if needed:
            </p>
            <div className="space-y-1 text-sm text-red-700">
              <p>• Fleet Control: +27 11 123 4567</p>
              <p>• Emergency Services: 10111</p>
              <p>• Breakdown Assistance: +27 11 765 4321</p>
            </div>
          </div>
        )}

        {/* Submit Button */}
        <div className="pt-4">
          <button
            type="submit"
            className="w-full bg-red-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
          >
            Submit Issue Report
          </button>
        </div>
      </form>
    </div>
  );
};

export default ReportIssuePage;
