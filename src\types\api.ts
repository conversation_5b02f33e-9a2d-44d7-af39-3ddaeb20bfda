// Base API Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  timestamp: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  message?: string;
  timestamp: string;
}

export interface ApiError {
  error: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
  error_id?: string;
  field_errors?: Record<string, string[]>;
}

// Vehicle Types
export interface VehicleRequest {
  registration_number: string;
  make: string;
  model: string;
  year: number;
  department: string;
  vin?: string;
  engine_number?: string;
  fuel_type?: 'petrol' | 'diesel' | 'electric' | 'hybrid';
  vehicle_type?: string;
  color?: string;
  current_mileage?: number;
  purchase_date?: string;
  purchase_price?: number;
  insurance_expiry?: string;
  license_expiry?: string;
  assigned_driver_id?: string;
  location?: string;
}

export interface VehicleResponse {
  assigned_driver: any;
  id: string;
  registration_number: string;
  make: string;
  model: string;
  year: number;
  status: 'active' | 'maintenance' | 'retired' | 'accident';
  department: string;
  vin?: string;
  engine_number?: string;
  fuel_type?: string;
  vehicle_type?: string;
  color?: string;
  current_mileage?: number;
  mileage?: number;
  last_service_date?: string;
  next_service_date?: string;
  insurance_expiry?: string;
  license_expiry?: string;
  purchase_date?: string;
  purchase_price?: number;
  current_value?: number;
  assigned_driver_id?: string;
  location?: string;
  created_at: string;
  updated_at: string;
}

// Work Order Types
export interface WorkOrderRequest {
  vehicle_id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'maintenance' | 'repair' | 'inspection' | 'accident';
  requested_by: string;
  due_date?: string;
}

export interface WorkOrderResponse {
  id: string;
  vehicle_id: string;
  vehicle: VehicleResponse;
  title: string;
  description: string;
  status: 'pending' | 'approved' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: string;
  requested_by: string;
  assigned_to?: string;
  estimated_cost?: number;
  actual_cost?: number;
  created_at: string;
  updated_at: string;
  due_date?: string;
  completed_at?: string;
}

// User Types
export interface UserResponse {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  department: string;
  phone?: string;
  is_active: boolean;
  created_at: string;
  last_login?: string;
}

// Query Filter Types
export interface VehicleFilters {
  page?: number;
  limit?: number;
  department?: string;
  status?: string;
  search?: string;
  make?: string;
  year_from?: number;
  year_to?: number;
}

export interface WorkOrderFilters {
  page?: number;
  limit?: number;
  status?: string;
  priority?: string;
  category?: string;
  vehicle_id?: string;
  assigned_to?: string;
  date_from?: string;
  date_to?: string;
}

// Loading States
export interface LoadingState {
  isLoading: boolean;
  isError: boolean;
  error?: ApiError;
  isFetching?: boolean;
}

// Form Data Types
export type VehicleFormData = Omit<VehicleRequest, 'id'>;
export type WorkOrderFormData = Omit<WorkOrderRequest, 'id'>;

// API Mutation Results
export interface MutationResult<T> {
  data?: T;
  error?: ApiError;
  isLoading: boolean;
  isSuccess: boolean;
  isError: boolean;
}



