import React from 'react';
import { Alert, AlertDescription } from './alert';
import { Wifi, WifiOff } from 'lucide-react';

interface Props {
  networkStatus: 'online' | 'offline' | 'slow';
}

export const NetworkStatusIndicator: React.FC<Props> = ({ networkStatus }) => {
  if (networkStatus === 'offline') {
    return (
      <Alert variant="destructive" className="mb-4">
        <WifiOff className="h-4 w-4" />
        <AlertDescription>
          You're currently offline. Some features may not work properly.
        </AlertDescription>
      </Alert>
    );
  }
  
  if (networkStatus === 'slow') {
    return (
      <Alert variant="default" className="mb-4">
        <Wifi className="h-4 w-4" />
        <AlertDescription>
          Slow network detected. Please be patient while we load your data.
        </AlertDescription>
      </Alert>
    );
  }
  
  return null;
};
