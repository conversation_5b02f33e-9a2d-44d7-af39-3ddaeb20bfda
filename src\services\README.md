# Services Directory Structure

## Current Structure (What I Created)
src/
├── store/api/           # RTK Query API definitions
│   ├── baseQuery.ts     # Base query with error handling
│   ├── vehicleApi.ts    # Vehicle-specific endpoints
│   ├── workOrderApi.ts  # Work order endpoints
│   └── ...
└── services/
    └── apiService.ts    # Base API configuration

## Recommended Services Structure (Following Rules)
src/
├── services/
│   ├── api/             # API service layer
│   │   ├── base.ts      # Base API client
│   │   ├── auth.ts      # Authentication service
│   │   ├── vehicles.ts  # Vehicle API service
│   │   ├── workOrders.ts# Work order API service
│   │   └── ...
│   ├── storage/         # Local storage services
│   │   ├── cache.ts     # Cache management
│   │   ├── offline.ts   # Offline data storage
│   │   └── preferences.ts# User preferences
│   ├── validation/      # Data validation services
│   │   ├── schemas.ts   # Validation schemas
│   │   └── validators.ts# Custom validators
│   ├── utils/           # Utility services
│   │   ├── dateUtils.ts # Date formatting
│   │   ├── fileUtils.ts # File handling
│   │   └── formatters.ts# Data formatters
│   └── external/        # External service integrations
│       ├── rtmc.ts      # RTMC API integration
│       ├── banking.ts   # Banking API integration
│       └── telematics.ts# Telematics integration